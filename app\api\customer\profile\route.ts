import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import jwt from "jsonwebtoken";

export const dynamic = "force-dynamic";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    return jwt.verify(token, JWT_SECRET) as {
      customerId: number;
      email: string;
    };
  } catch (error) {
    return null;
  }
}

export async function GET(request: NextRequest) {
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const result = await query(
      "SELECT id, name, email, phone, created_at FROM customers WHERE id = $1",
      [decoded.customerId]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { message: "Customer not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error("Get customer profile error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function PUT(request: NextRequest) {
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { name, phone } = await request.json();

    const result = await query(
      `UPDATE customers
       SET name = $1, phone = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3
       RETURNING id, name, email, phone, created_at`,
      [name, phone || null, decoded.customerId]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { message: "Customer not found" },
        { status: 404 }
      );
    }

    return NextResponse.json(result.rows[0]);
  } catch (error) {
    console.error("Update customer profile error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Delete customer and all related data
    await query("BEGIN");

    try {
      // Delete payments
      await query("DELETE FROM payments WHERE customer_id = $1", [
        decoded.customerId,
      ]);

      // Delete bookings
      await query("DELETE FROM bookings WHERE customer_id = $1", [
        decoded.customerId,
      ]);

      // Delete notifications
      await query("DELETE FROM notifications WHERE customer_id = $1", [
        decoded.customerId,
      ]);

      // Delete customer
      const result = await query("DELETE FROM customers WHERE id = $1", [
        decoded.customerId,
      ]);

      if (result.rowCount === 0) {
        await query("ROLLBACK");
        return NextResponse.json(
          { message: "Customer not found" },
          { status: 404 }
        );
      }

      await query("COMMIT");

      return NextResponse.json({ message: "Account deleted successfully" });
    } catch (error) {
      await query("ROLLBACK");
      throw error;
    }
  } catch (error) {
    console.error("Delete customer account error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
