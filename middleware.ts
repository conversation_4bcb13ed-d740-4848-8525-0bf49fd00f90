import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";

// Routes that require customer authentication
const protectedCustomerRoutes = [
  "/customer/dashboard",
  "/customer/booking",
  "/customer/profile",
  "/customer/payments",
];

// Routes that require admin authentication (exclude login page)
const protectedAdminRoutes = ["/admin"];
const adminLoginRoute = "/admin/login";

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  console.log("🛡️ Middleware checking path:", pathname);

  // Check if it's a protected customer route
  if (protectedCustomerRoutes.some((route) => pathname.startsWith(route))) {
    const token =
      request.cookies.get("customer_token")?.value ||
      request.headers.get("authorization")?.replace("Bearer ", "");

    if (!token || token.length < 10) {
      return NextResponse.redirect(new URL("/customer/auth", request.url));
    }

    return NextResponse.next();
  }

  // Check if it's a protected admin route (but not the login page)
  if (
    protectedAdminRoutes.some((route) => pathname.startsWith(route)) &&
    !pathname.startsWith(adminLoginRoute)
  ) {
    console.log("🛡️ Checking admin route protection for:", pathname);

    const token =
      request.cookies.get("admin_token")?.value ||
      request.headers.get("authorization")?.replace("Bearer ", "");

    console.log("🛡️ Admin token found:", token ? "Yes" : "No");
    console.log("🛡️ Token value:", token);

    if (!token) {
      console.log("🛡️ No token, redirecting to login");
      return NextResponse.redirect(new URL("/admin/login", request.url));
    }

    // Simple token validation (for demo purposes)
    if (token.startsWith("admin-session-")) {
      console.log("🛡️ Valid admin token, allowing access");
      return NextResponse.next();
    } else {
      console.log("🛡️ Invalid admin token, redirecting to login");
      return NextResponse.redirect(new URL("/admin/login", request.url));
    }
  }

  return NextResponse.next();
}

export const config = {
  matcher: [
    "/customer/dashboard/:path*",
    "/customer/booking/:path*",
    "/customer/profile/:path*",
    "/admin/:path*",
  ],
};
