-- Connect to the database (it's already created by docker-compose)
\c freedom_travel;

-- Create customers table
CREATE TABLE IF NOT EXISTS customers (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(50),
    password_hash VARCHAR(255) NOT NULL,
    email_verified BOOLEAN DEFAULT FALSE,
    verification_token VARCHAR(255),
    reset_token VARCHAR(255),
    reset_token_expires TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create bookings table (updated with customer_id)
CREATE TABLE IF NOT EXISTS bookings (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    phone VARCHAR(50),
    destination VARCHAR(255) NOT NULL,
    start_date DATE,
    end_date DATE,
    travelers INTEGER DEFAULT 1,
    requirements TEXT,
    status VARCHAR(50) DEFAULT 'pending',
    price DECIMAL(10,2) DEFAULT 0,
    payment_status VARCHAR(50) DEFAULT 'unpaid',
    payment_method VARCHAR(50),
    payment_id VARCHAR(255),
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id SERIAL PRIMARY KEY,
    booking_id INTEGER REFERENCES bookings(id) ON DELETE CASCADE,
    customer_id INTEGER REFERENCES customers(id) ON DELETE CASCADE,
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'USD',
    payment_method VARCHAR(50) NOT NULL,
    payment_provider VARCHAR(50) NOT NULL,
    provider_payment_id VARCHAR(255),
    status VARCHAR(50) DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create notifications table
CREATE TABLE IF NOT EXISTS notifications (
    id SERIAL PRIMARY KEY,
    customer_id INTEGER REFERENCES customers(id) ON DELETE CASCADE,
    booking_id INTEGER REFERENCES bookings(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL,
    title VARCHAR(255) NOT NULL,
    message TEXT NOT NULL,
    read BOOLEAN DEFAULT FALSE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create contact_messages table
CREATE TABLE IF NOT EXISTS contact_messages (
    id SERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL,
    subject VARCHAR(255),
    message TEXT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create blog_posts table
CREATE TABLE IF NOT EXISTS blog_posts (
    id SERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    content TEXT NOT NULL,
    excerpt TEXT,
    image_url VARCHAR(500),
    author VARCHAR(255) DEFAULT 'Admin',
    published BOOLEAN DEFAULT true,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Insert sample customers (password is 'password123' hashed with bcrypt)
INSERT INTO customers (name, email, phone, password_hash, email_verified) VALUES
('João Silva', '<EMAIL>', '+55 11 99999-9999', '$2b$10$rOvHPxfuqjNNPvEHXvS0/.OTSpieqjLWq5HlqRr6PiRdHOpo9Stw.', TRUE),
('Maria Santos', '<EMAIL>', '+55 21 88888-8888', '$2b$10$rOvHPxfuqjNNPvEHXvS0/.OTSpieqjLWq5HlqRr6PiRdHOpo9Stw.', TRUE),
('Carlos Oliveira', '<EMAIL>', '+55 31 77777-7777', '$2b$10$rOvHPxfuqjNNPvEHXvS0/.OTSpieqjLWq5HlqRr6PiRdHOpo9Stw.', TRUE);

-- Insert sample bookings with customer_id and pricing
INSERT INTO bookings (customer_id, name, email, phone, destination, start_date, end_date, travelers, requirements, status, price, payment_status) VALUES
(1, 'João Silva', '<EMAIL>', '+55 11 99999-9999', 'Paris, França', '2024-08-15', '2024-08-22', 2, 'Lua de mel, preferência por hotéis românticos', 'pending', 2500.00, 'unpaid'),
(2, 'Maria Santos', '<EMAIL>', '+55 21 88888-8888', 'Tokyo, Japão', '2024-09-10', '2024-09-20', 1, 'Interessada em cultura japonesa e gastronomia', 'confirmed', 3200.00, 'paid'),
(3, 'Carlos Oliveira', '<EMAIL>', '+55 31 77777-7777', 'New York, EUA', '2024-07-25', '2024-08-02', 4, 'Viagem em família com crianças', 'pending', 4800.00, 'unpaid');

INSERT INTO contact_messages (name, email, subject, message) VALUES
('Ana Costa', '<EMAIL>', 'Dúvida sobre pacotes', 'Gostaria de saber mais sobre os pacotes para a Europa.'),
('Pedro Lima', '<EMAIL>', 'Cancelamento de viagem', 'Preciso cancelar minha viagem marcada para o próximo mês.'),
('Lucia Ferreira', '<EMAIL>', 'Elogio ao atendimento', 'Excelente atendimento! Muito satisfeita com o serviço.');

INSERT INTO blog_posts (title, content, excerpt, image_url, author, published) VALUES
('As 10 Melhores Praias do Brasil',
'O Brasil possui algumas das praias mais belas do mundo. Neste artigo, vamos explorar as 10 melhores praias que você deve visitar em sua próxima viagem pelo país.

1. **Praia de Jericoacoara - Ceará**
Uma das praias mais famosas do Brasil, conhecida por suas dunas e ventos ideais para windsurf.

2. **Fernando de Noronha - Pernambuco**
Arquipélago paradisíaco com águas cristalinas e vida marinha exuberante.

3. **Praia do Sancho - Fernando de Noronha**
Eleita várias vezes como a melhor praia do mundo pelo TripAdvisor.

4. **Praia de Copacabana - Rio de Janeiro**
Ícone mundial, famosa por sua energia vibrante e paisagem deslumbrante.

5. **Praia de Ipanema - Rio de Janeiro**
Conhecida mundialmente pela música "Garota de Ipanema" e seu pôr do sol espetacular.',
'Descubra as praias mais incríveis do Brasil, desde Jericoacoara até Fernando de Noronha.',
'https://images.unsplash.com/photo-1544551763-46a013bb70d5?w=800',
'Admin',
true),

('Guia Completo: Como Planejar sua Primeira Viagem Internacional',
'Planejar sua primeira viagem internacional pode parecer intimidador, mas com as dicas certas, você pode tornar essa experiência inesquecível e sem estresse.

## Documentação Necessária
- **Passaporte**: Verifique a validade (mínimo 6 meses)
- **Visto**: Pesquise se é necessário para seu destino
- **Vacinas**: Consulte os requisitos sanitários

## Planejamento Financeiro
- Pesquise a moeda local
- Considere taxas de câmbio
- Planeje um orçamento para emergências

## Escolha do Destino
Para iniciantes, recomendamos:
- Europa (fácil locomoção entre países)
- Canadá (cultura similar, idioma acessível)
- Chile ou Argentina (proximidade cultural)

## Dicas de Segurança
- Faça cópias dos documentos
- Contrate seguro viagem
- Pesquise sobre o destino',
'Tudo que você precisa saber para planejar sua primeira aventura internacional com segurança e tranquilidade.',
'https://images.unsplash.com/photo-1488646953014-85cb44e25828?w=800',
'Admin',
true),

('Destinos Imperdíveis para 2024',
'O ano de 2024 promete ser incrível para os amantes de viagem. Confira nossa seleção de destinos que estão em alta e prometem experiências únicas.

## 1. Portugal
Com sua rica história, gastronomia excepcional e paisagens deslumbrantes, Portugal continua sendo um dos destinos mais procurados.

**Destaques:**
- Lisboa e Porto
- Região do Douro
- Algarve

## 2. Japão
Após a reabertura completa, o Japão volta a receber turistas com toda sua magia cultural.

**Experiências únicas:**
- Temporada das cerejeiras (sakura)
- Cultura milenar
- Gastronomia autêntica

## 3. Costa Rica
Para os amantes da natureza, a Costa Rica oferece biodiversidade incomparável.

**Atividades:**
- Observação de vida selvagem
- Vulcões ativos
- Praias paradisíacas',
'Conheça os destinos que prometem ser tendência em 2024 e comece a planejar sua próxima aventura.',
'https://images.unsplash.com/photo-1469474968028-56623f02e42e?w=800',
'Admin',
true);

-- Create indexes for better performance
CREATE INDEX idx_bookings_status ON bookings(status);
CREATE INDEX idx_bookings_created_at ON bookings(created_at);
CREATE INDEX idx_blog_posts_published ON blog_posts(published);
CREATE INDEX idx_contact_messages_created_at ON contact_messages(created_at);
