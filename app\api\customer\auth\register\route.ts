import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import crypto from "crypto";

export const dynamic = "force-dynamic";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export async function POST(request: NextRequest) {
  try {
    const { name, email, phone, password } = await request.json();

    if (!name || !email || !password) {
      return NextResponse.json(
        { message: "Name, email, and password are required" },
        { status: 400 }
      );
    }

    // Check if customer already exists
    const existingCustomer = await query(
      "SELECT id FROM customers WHERE email = $1",
      [email]
    );

    if (existingCustomer.rows.length > 0) {
      return NextResponse.json(
        { message: "Customer with this email already exists" },
        { status: 409 }
      );
    }

    // Hash password
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    // Create customer
    const result = await query(
      `INSERT INTO customers (name, email, phone, password_hash)
       VALUES ($1, $2, $3, $4)
       RETURNING id, name, email, phone, created_at`,
      [name, email, phone || null, passwordHash]
    );

    const customer = result.rows[0];

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString("hex");

    // Update customer with verification token
    await query(
      `UPDATE customers
       SET verification_token = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2`,
      [verificationToken, customer.id]
    );

    // Create verification link
    const verificationLink = `${
      process.env.NEXT_PUBLIC_BASE_URL || "http://localhost:3000"
    }/customer/verify-email?token=${verificationToken}`;

    console.log(`📧 EMAIL VERIFICATION LINK for ${customer.email}:`);
    console.log(`🔗 ${verificationLink}`);

    // Generate JWT token
    const token = jwt.sign(
      { customerId: customer.id, email: customer.email },
      JWT_SECRET,
      { expiresIn: "7d" }
    );

    return NextResponse.json({
      token,
      customer,
      message:
        "Registration successful. Please check your email to verify your account.",
      // In development, include the verification link
      ...(process.env.NODE_ENV === "development" && {
        verificationLink,
        note: "In development mode - check console for verification link",
      }),
    });
  } catch (error) {
    console.error("Customer registration error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
