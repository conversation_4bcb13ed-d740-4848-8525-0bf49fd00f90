import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import jwt from "jsonwebtoken";

export const dynamic = "force-dynamic";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    return jwt.verify(token, JWT_SECRET) as { customerId: number; email: string };
  } catch (error) {
    return null;
  }
}

// Update booking
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const {
      destination,
      startDate,
      endDate,
      travelers,
      requirements,
    } = await request.json();

    const bookingId = parseInt(id);

    // Check if booking belongs to customer and can be modified
    const checkResult = await query(
      "SELECT status FROM bookings WHERE id = $1 AND customer_id = $2",
      [bookingId, decoded.customerId]
    );

    if (checkResult.rows.length === 0) {
      return NextResponse.json(
        { message: "Booking not found" },
        { status: 404 }
      );
    }

    const currentStatus = checkResult.rows[0].status;
    if (currentStatus === "confirmed" || currentStatus === "completed") {
      return NextResponse.json(
        { message: "Cannot modify confirmed or completed bookings" },
        { status: 400 }
      );
    }

    // Calculate new price
    const days = Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24));
    const weeks = Math.max(1, Math.ceil(days / 7));
    const basePrice = travelers * weeks * 1000;

    const result = await query(
      `UPDATE bookings 
       SET destination = $1, start_date = $2, end_date = $3, travelers = $4, requirements = $5, price = $6, updated_at = CURRENT_TIMESTAMP
       WHERE id = $7 AND customer_id = $8
       RETURNING *`,
      [destination, startDate, endDate, travelers, requirements || "", basePrice, bookingId, decoded.customerId]
    );

    if (result.rows.length === 0) {
      return NextResponse.json(
        { message: "Failed to update booking" },
        { status: 400 }
      );
    }

    return NextResponse.json({
      booking: result.rows[0],
      message: "Booking updated successfully",
    });
  } catch (error) {
    console.error("Update booking error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Cancel booking
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const bookingId = parseInt(id);

    // Check if booking belongs to customer
    const checkResult = await query(
      "SELECT status FROM bookings WHERE id = $1 AND customer_id = $2",
      [bookingId, decoded.customerId]
    );

    if (checkResult.rows.length === 0) {
      return NextResponse.json(
        { message: "Booking not found" },
        { status: 404 }
      );
    }

    const currentStatus = checkResult.rows[0].status;
    if (currentStatus === "completed") {
      return NextResponse.json(
        { message: "Cannot cancel completed bookings" },
        { status: 400 }
      );
    }

    // Update status to cancelled instead of deleting
    const result = await query(
      `UPDATE bookings 
       SET status = 'cancelled', updated_at = CURRENT_TIMESTAMP
       WHERE id = $1 AND customer_id = $2
       RETURNING *`,
      [bookingId, decoded.customerId]
    );

    return NextResponse.json({
      booking: result.rows[0],
      message: "Booking cancelled successfully",
    });
  } catch (error) {
    console.error("Cancel booking error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
