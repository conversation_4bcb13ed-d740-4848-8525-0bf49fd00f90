import { NextRequest, NextResponse } from "next/server";

export const dynamic = "force-dynamic";

// Cache for exchange rates (valid for 1 hour)
let cachedRates: { [key: string]: number } | null = null;
let cacheTimestamp: number = 0;
const CACHE_DURATION = 60 * 60 * 1000; // 1 hour in milliseconds

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const baseCurrency = searchParams.get("base") || "USD";
    const targetCurrency = searchParams.get("target");

    // Check if we have cached rates that are still valid
    const now = Date.now();
    if (cachedRates && (now - cacheTimestamp) < CACHE_DURATION) {
      if (targetCurrency) {
        const rate = cachedRates[targetCurrency];
        if (rate) {
          return NextResponse.json({ 
            base: baseCurrency,
            target: targetCurrency,
            rate: rate,
            cached: true,
            timestamp: cacheTimestamp
          });
        }
      } else {
        return NextResponse.json({
          base: baseCurrency,
          rates: cachedRates,
          cached: true,
          timestamp: cacheTimestamp
        });
      }
    }

    // Fetch fresh rates from ExchangeRate-API (free tier)
    const response = await fetch(
      `https://api.exchangerate-api.com/v4/latest/${baseCurrency}`,
      {
        headers: {
          'User-Agent': 'Freedom-to-Travel-App/1.0'
        }
      }
    );

    if (!response.ok) {
      throw new Error(`Exchange rate API error: ${response.status}`);
    }

    const data = await response.json();
    
    // Update cache
    cachedRates = data.rates;
    cacheTimestamp = now;

    if (targetCurrency) {
      const rate = data.rates[targetCurrency];
      if (!rate) {
        return NextResponse.json(
          { error: `Currency ${targetCurrency} not supported` },
          { status: 400 }
        );
      }

      return NextResponse.json({
        base: baseCurrency,
        target: targetCurrency,
        rate: rate,
        cached: false,
        timestamp: now
      });
    }

    return NextResponse.json({
      base: baseCurrency,
      rates: data.rates,
      cached: false,
      timestamp: now
    });

  } catch (error) {
    console.error("Exchange rate API error:", error);
    
    // Fallback to approximate rates if API fails
    const fallbackRates = {
      USD: 1,
      EUR: 0.85,
      NOK: 10.5,
      GBP: 0.75
    };

    const { searchParams } = new URL(request.url);
    const baseCurrency = searchParams.get("base") || "USD";
    const targetCurrency = searchParams.get("target");

    if (targetCurrency) {
      return NextResponse.json({
        base: baseCurrency,
        target: targetCurrency,
        rate: fallbackRates[targetCurrency as keyof typeof fallbackRates] || 1,
        cached: false,
        fallback: true,
        error: "Using fallback rates due to API error"
      });
    }

    return NextResponse.json({
      base: baseCurrency,
      rates: fallbackRates,
      cached: false,
      fallback: true,
      error: "Using fallback rates due to API error"
    });
  }
}
