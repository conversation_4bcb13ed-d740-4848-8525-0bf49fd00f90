import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import crypto from "crypto";

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const { email } = await request.json();

    if (!email) {
      return NextResponse.json(
        { message: "Email is required" },
        { status: 400 }
      );
    }

    // Check if customer exists
    const customerResult = await query(
      "SELECT id, name, email FROM customers WHERE email = $1",
      [email]
    );

    if (customerResult.rows.length === 0) {
      // Don't reveal if email exists or not for security
      return NextResponse.json({
        message: "If an account with this email exists, you will receive a password reset link.",
      });
    }

    const customer = customerResult.rows[0];

    // Generate reset token
    const resetToken = crypto.randomBytes(32).toString("hex");
    const resetTokenExpires = new Date(Date.now() + 3600000); // 1 hour from now

    // Save reset token to database
    await query(
      `UPDATE customers 
       SET reset_token = $1, reset_token_expires = $2, updated_at = CURRENT_TIMESTAMP
       WHERE id = $3`,
      [resetToken, resetTokenExpires, customer.id]
    );

    // In a real application, you would send an email here
    // For now, we'll just log the reset link
    const resetLink = `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/customer/reset-password?token=${resetToken}`;
    
    console.log(`🔑 PASSWORD RESET LINK for ${email}:`);
    console.log(`🔗 ${resetLink}`);
    console.log(`⏰ Expires: ${resetTokenExpires}`);

    // TODO: Send email with reset link
    // await sendPasswordResetEmail(customer.email, customer.name, resetLink);

    return NextResponse.json({
      message: "If an account with this email exists, you will receive a password reset link.",
      // In development, include the reset link
      ...(process.env.NODE_ENV === 'development' && { 
        resetLink,
        note: "In development mode - check console for reset link" 
      })
    });

  } catch (error) {
    console.error("Forgot password error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
