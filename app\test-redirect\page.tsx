"use client";

import { useRouter } from "next/navigation";
import { useEffect } from "react";
import { Button } from "@/components/ui/button";

export default function TestRedirectPage() {
  const router = useRouter();

  useEffect(() => {
    // Set a valid token from the latest login
    const testToken =
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************************************.9vomWKVJYA-93IpEat2UrLko6rhZ9uGbh_6TUCxE5XM";

    localStorage.setItem("customer_token", testToken);
    document.cookie = `customer_token=${testToken}; path=/; max-age=${
      7 * 24 * 60 * 60
    }; samesite=strict`;

    console.log("🧪 Test token set:", testToken);
  }, []);

  const handleRedirect = () => {
    console.log("🚀 Redirecting to dashboard...");
    window.location.href = "/customer/dashboard";
  };

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <h1 className="text-2xl font-bold mb-4">Test Redirect</h1>
        <Button onClick={handleRedirect}>Go to Customer Dashboard</Button>
      </div>
    </div>
  );
}
