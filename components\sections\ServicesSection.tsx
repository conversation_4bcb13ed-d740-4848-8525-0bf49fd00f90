"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Bed, Car, Map, Headphones } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

export default function ServicesSection() {
  const { t } = useLanguage();

  const services = [
    {
      icon: Bed,
      titleKey: "services.accommodation.title",
      descriptionKey: "services.accommodation.description",
    },
    {
      icon: Car,
      titleKey: "services.transport.title",
      descriptionKey: "services.transport.description",
    },
    {
      icon: Map,
      titleKey: "services.itinerary.title",
      descriptionKey: "services.itinerary.description",
    },
    {
      icon: Headphones,
      titleKey: "services.support.title",
      descriptionKey: "services.support.description",
    },
  ];

  const scrollToBooking = () => {
    const element = document.getElementById("booking");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section id="services" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-5xl font-bold text-gray-900 mb-6">
            {t("services.title")}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("services.subtitle")}
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
          {services.map((service, index) => {
            const Icon = service.icon;
            return (
              <Card
                key={index}
                className="bg-white border-slate-200 hover:shadow-lg transition-all duration-300 transform hover:scale-105"
              >
                <CardContent className="p-6 text-center">
                  <div
                    className="w-16 h-16 mx-auto mb-4 rounded-full flex items-center justify-center"
                    style={{ backgroundColor: "#f00d45" }}
                  >
                    <Icon className="h-8 w-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-gray-900 mb-3">
                    {t(service.titleKey)}
                  </h3>
                  <p className="text-gray-600 leading-relaxed">
                    {t(service.descriptionKey)}
                  </p>
                </CardContent>
              </Card>
            );
          })}
        </div>

        <div className="text-center">
          <Button
            onClick={scrollToBooking}
            size="lg"
            className="text-white font-semibold px-8 py-4 rounded-md transition-all duration-300 transform hover:scale-105"
            style={{
              backgroundColor: "#f00d45",
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.backgroundColor = "#d00b3a";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.backgroundColor = "#f00d45";
            }}
          >
            {t("common.bookNow")}
          </Button>
        </div>
      </div>
    </section>
  );
}
