"use client";

import { useEffect } from "react";

export function useCenterModal(isOpen: boolean) {
  useEffect(() => {
    if (!isOpen) return;

    const centerModal = () => {
      // Find all dialog content elements
      const dialogs = document.querySelectorAll("[data-radix-dialog-content]");

      dialogs.forEach((dialog) => {
        const element = dialog as HTMLElement;

        // Force centering styles
        element.style.position = "fixed";
        element.style.left = "50%";
        element.style.top = "50%";
        element.style.transform = "translate(-50%, -50%)";
        element.style.margin = "0";
        element.style.right = "auto";
        element.style.bottom = "auto";

        // Set responsive width based on screen size
        if (window.innerWidth <= 320) {
          // Very small screens
          element.style.width = "88vw";
          element.style.maxWidth = "280px";
          element.style.maxHeight = "85vh";
          element.style.overflowY = "auto";
          element.style.padding = "0.75rem";
        } else if (window.innerWidth <= 480) {
          // Small screens
          element.style.width = "92vw";
          element.style.maxWidth = "340px";
          element.style.maxHeight = "90vh";
          element.style.overflowY = "auto";
          element.style.padding = "1rem";
        } else if (window.innerWidth <= 768) {
          // Medium screens
          element.style.width = "95vw";
          element.style.maxWidth = "350px";
          element.style.maxHeight = "90vh";
          element.style.overflowY = "auto";
        }

        // Add force-center class
        element.classList.add("force-center-modal");
      });
    };

    // Center immediately
    centerModal();

    // Center on resize
    window.addEventListener("resize", centerModal);

    // Use MutationObserver to catch any dynamic changes
    const observer = new MutationObserver(centerModal);
    observer.observe(document.body, {
      childList: true,
      subtree: true,
      attributes: true,
      attributeFilter: ["style", "class"],
    });

    return () => {
      window.removeEventListener("resize", centerModal);
      observer.disconnect();
    };
  }, [isOpen]);
}
