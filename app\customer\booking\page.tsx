"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { useCustomerAuth } from "@/contexts/CustomerAuthContext";
import { Calendar, Users, MapPin, ArrowLeft } from "lucide-react";
import { toast } from "sonner";

export default function NewBookingPage() {
  const router = useRouter();
  const { customer } = useCustomerAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState({
    destination: "",
    startDate: "",
    endDate: "",
    travelers: 1,
    requirements: "",
  });

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: name === "travelers" ? parseInt(value) || 1 : value,
    }));
  };

  const calculatePrice = () => {
    if (!formData.startDate || !formData.endDate) return 0;

    const days = Math.ceil(
      (new Date(formData.endDate).getTime() -
        new Date(formData.startDate).getTime()) /
        (1000 * 60 * 60 * 24)
    );
    const weeks = Math.max(1, Math.ceil(days / 7));
    return formData.travelers * weeks * 1000;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!customer) {
      toast.error("Please log in to create a booking");
      router.push("/customer/auth");
      return;
    }

    setIsSubmitting(true);

    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/customer/bookings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        const data = await response.json();
        toast.success("Booking created successfully!");
        router.push("/customer/dashboard");
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to create booking");
      }
    } catch (error) {
      console.error("Booking creation error:", error);
      toast.error("Failed to create booking. Please try again.");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!customer) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="p-6 text-center">
            <p className="text-gray-600 mb-4">
              Please log in to create a booking
            </p>
            <Button onClick={() => router.push("/customer/auth")}>
              Go to Login
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  const estimatedPrice = calculatePrice();

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center h-16">
            <Button
              onClick={() => router.push("/customer/dashboard")}
              variant="ghost"
              className="mr-4"
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Dashboard
            </Button>
            <h1 className="text-xl font-semibold text-gray-900">
              Create New Booking
            </h1>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Booking Form */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle>Booking Details</CardTitle>
              </CardHeader>
              <CardContent>
                <form onSubmit={handleSubmit} className="space-y-6">
                  <div>
                    <Label
                      htmlFor="destination"
                      className="font-semibold text-gray-700"
                    >
                      Destination
                    </Label>
                    <div className="relative mt-2">
                      <MapPin className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="destination"
                        name="destination"
                        type="text"
                        value={formData.destination}
                        onChange={handleInputChange}
                        placeholder="Where would you like to go?"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                      <Label
                        htmlFor="startDate"
                        className="font-semibold text-gray-700"
                      >
                        Start Date
                      </Label>
                      <div className="relative mt-2">
                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="startDate"
                          name="startDate"
                          type="date"
                          value={formData.startDate}
                          onChange={handleInputChange}
                          className="pl-10"
                          min={new Date().toISOString().split("T")[0]}
                          required
                        />
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="endDate"
                        className="font-semibold text-gray-700"
                      >
                        End Date
                      </Label>
                      <div className="relative mt-2">
                        <Calendar className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                        <Input
                          id="endDate"
                          name="endDate"
                          type="date"
                          value={formData.endDate}
                          onChange={handleInputChange}
                          className="pl-10"
                          min={
                            formData.startDate ||
                            new Date().toISOString().split("T")[0]
                          }
                          required
                        />
                      </div>
                    </div>
                  </div>

                  <div>
                    <Label
                      htmlFor="travelers"
                      className="font-semibold text-gray-700"
                    >
                      Number of Travelers
                    </Label>
                    <div className="relative mt-2">
                      <Users className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        id="travelers"
                        name="travelers"
                        type="number"
                        value={formData.travelers}
                        onChange={handleInputChange}
                        min="1"
                        max="20"
                        className="pl-10"
                        required
                      />
                    </div>
                  </div>

                  <div>
                    <Label
                      htmlFor="requirements"
                      className="font-semibold text-gray-700"
                    >
                      Special Requirements (Optional)
                    </Label>
                    <Textarea
                      id="requirements"
                      name="requirements"
                      value={formData.requirements}
                      onChange={handleInputChange}
                      placeholder="Any special requests, dietary requirements, accessibility needs, etc."
                      className="mt-2"
                      rows={4}
                    />
                  </div>

                  <Button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-blue-600 hover:bg-blue-700"
                  >
                    {isSubmitting ? "Creating Booking..." : "Create Booking"}
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>

          {/* Booking Summary */}
          <div className="lg:col-span-1">
            <Card className="sticky top-8">
              <CardHeader>
                <CardTitle>Booking Summary</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div>
                  <p className="text-sm text-gray-600">Customer</p>
                  <p className="font-medium">{customer.name}</p>
                  <p className="text-sm text-gray-500">{customer.email}</p>
                </div>

                {formData.destination && (
                  <div>
                    <p className="text-sm text-gray-600">Destination</p>
                    <p className="font-medium">{formData.destination}</p>
                  </div>
                )}

                {formData.startDate && formData.endDate && (
                  <div>
                    <p className="text-sm text-gray-600">Travel Dates</p>
                    <p className="font-medium">
                      {new Date(formData.startDate).toLocaleDateString()} -{" "}
                      {new Date(formData.endDate).toLocaleDateString()}
                    </p>
                    <p className="text-xs text-gray-500">
                      {Math.ceil(
                        (new Date(formData.endDate).getTime() -
                          new Date(formData.startDate).getTime()) /
                          (1000 * 60 * 60 * 24)
                      )}{" "}
                      days
                    </p>
                  </div>
                )}

                <div>
                  <p className="text-sm text-gray-600">Travelers</p>
                  <p className="font-medium">
                    {formData.travelers}{" "}
                    {formData.travelers === 1 ? "person" : "people"}
                  </p>
                </div>

                {estimatedPrice > 0 && (
                  <div className="border-t pt-4">
                    <div className="flex justify-between items-center">
                      <p className="text-sm text-gray-600">Estimated Price</p>
                      <p className="text-lg font-bold text-blue-600">
                        ${estimatedPrice.toLocaleString()}
                      </p>
                    </div>
                    <p className="text-xs text-gray-500 mt-1">
                      Final price will be confirmed by our team
                    </p>
                  </div>
                )}

                <div className="bg-green-50 p-4 rounded-lg border border-green-200">
                  <h4 className="font-medium text-green-900 mb-2">
                    Instant Booking Process
                  </h4>
                  <ul className="text-sm text-green-800 space-y-1">
                    <li>• Your booking is automatically confirmed</li>
                    <li>• Proceed to secure payment immediately</li>
                    <li>• Receive instant confirmation & itinerary</li>
                    <li>• Start planning your adventure!</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </main>
    </div>
  );
}
