import { createClient } from "@libsql/client";

let db: any = null;

export function getDatabase() {
  if (!db) {
    db = createClient({
      url: ":memory:", // In-memory database for development
    });

    // Add prepare method for compatibility
    db = addPrepareMethod(db);

    // Initialize tables
    initializeTables();
  }

  return db;
}

// Helper function to simulate prepare for compatibility
function createPreparedStatement(db: any, sql: string) {
  return {
    all: async (params: any[] = []) => {
      try {
        const result = await db.execute({ sql, args: params });
        // Convert rows to plain objects with column names
        if (result.rows && result.columns) {
          return result.rows.map((row: any[]) => {
            const obj: any = {};
            result.columns.forEach((col: string, index: number) => {
              obj[col] = row[index];
            });
            return obj;
          });
        }
        return result.rows || [];
      } catch (error) {
        console.error("Database query error:", error);
        throw error;
      }
    },
    run: async (params: any[] = []) => {
      try {
        const result = await db.execute({ sql, args: params });
        return {
          lastInsertRowid: result.lastInsertRowid,
          changes: result.changes,
        };
      } catch (error) {
        console.error("Database execution error:", error);
        throw error;
      }
    },
  };
}

// Add prepare method to db object
function addPrepareMethod(dbInstance: any) {
  if (!dbInstance.prepare) {
    dbInstance.prepare = (sql: string) =>
      createPreparedStatement(dbInstance, sql);
  }
  return dbInstance;
}

function initializeTables() {
  const dbInstance = getDatabase();

  // Blog posts table
  dbInstance.execute({
    sql: `
    CREATE TABLE IF NOT EXISTS blog_posts (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      title TEXT NOT NULL,
      content TEXT NOT NULL,
      excerpt TEXT,
      author TEXT NOT NULL,
      slug TEXT UNIQUE NOT NULL,
      image TEXT,
      language TEXT DEFAULT 'en',
      published BOOLEAN DEFAULT false,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `,
    args: [],
  });

  // Bookings table
  dbInstance.execute({
    sql: `
    CREATE TABLE IF NOT EXISTS bookings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT NOT NULL,
      phone TEXT,
      destination TEXT,
      start_date DATE,
      end_date DATE,
      travelers INTEGER,
      requirements TEXT,
      status TEXT DEFAULT 'pending',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `,
    args: [],
  });

  // Contact messages table
  dbInstance.execute({
    sql: `
    CREATE TABLE IF NOT EXISTS contact_messages (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      name TEXT NOT NULL,
      email TEXT NOT NULL,
      message TEXT NOT NULL,
      status TEXT DEFAULT 'unread',
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `,
    args: [],
  });

  // Admin users table
  dbInstance.execute({
    sql: `
    CREATE TABLE IF NOT EXISTS admin_users (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      username TEXT UNIQUE NOT NULL,
      password_hash TEXT NOT NULL,
      email TEXT UNIQUE NOT NULL,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `,
    args: [],
  });

  // Admin settings table
  dbInstance.execute({
    sql: `
    CREATE TABLE IF NOT EXISTS admin_settings (
      id INTEGER PRIMARY KEY AUTOINCREMENT,
      general_settings TEXT,
      security_settings TEXT,
      appearance_settings TEXT,
      payment_settings TEXT,
      business_settings TEXT,
      created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
      updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
    )
  `,
    args: [],
  });

  // Insert sample blog posts
  insertSampleData();
}

// Export query function for API compatibility
export async function query(sql: string, params: any[] = []) {
  const dbInstance = getDatabase();

  try {
    const result = await dbInstance.execute({ sql, args: params });

    // Convert rows to plain objects with column names for compatibility
    if (result.rows && result.columns) {
      const rows = result.rows.map((row: any[]) => {
        const obj: any = {};
        result.columns.forEach((col: string, index: number) => {
          obj[col] = row[index];
        });
        return obj;
      });

      return {
        rows,
        rowCount: rows.length,
        lastInsertRowid: result.lastInsertRowid,
        changes: result.changes,
      };
    }

    return {
      rows: result.rows || [],
      rowCount: result.rows?.length || 0,
      lastInsertRowid: result.lastInsertRowid,
      changes: result.changes,
    };
  } catch (error) {
    console.error("Database query error:", error);
    throw error;
  }
}

function insertSampleData() {
  const dbInstance = getDatabase();

  // Sample blog posts
  const samplePosts = [
    {
      title: "Top 10 Wheelchair-Accessible Destinations in Europe",
      content:
        "Europe offers incredible accessible travel opportunities. From the cobblestone streets of Prague with modern accessibility features to the stunning coastlines of Portugal with accessible beaches, here are our top picks for wheelchair-friendly European destinations...",
      excerpt:
        "Discover the most wheelchair-accessible destinations across Europe, featuring modern facilities and breathtaking experiences.",
      author: "Sarah Johnson",
      slug: "wheelchair-accessible-europe-destinations",
      image:
        "https://images.pexels.com/photos/1141853/pexels-photo-1141853.jpeg?auto=compress&cs=tinysrgb&w=800",
      language: "en",
      published: true,
    },
    {
      title: "Essential Packing Tips for Accessible Travel",
      content:
        "Traveling with mobility equipment requires careful planning. Here's our comprehensive guide to packing for accessible travel, including must-have items, documentation tips, and how to prepare for different climates...",
      excerpt:
        "Learn how to pack efficiently for accessible travel with our expert tips and essential item checklist.",
      author: "Michael Chen",
      slug: "accessible-travel-packing-tips",
      image:
        "https://images.pexels.com/photos/2422915/pexels-photo-2422915.jpeg?auto=compress&cs=tinysrgb&w=800",
      language: "en",
      published: true,
    },
    {
      title: "Accessible Hotels: What to Look For",
      content:
        "Not all accessible hotels are created equal. Learn what questions to ask when booking, what features to look for, and how to ensure your accommodation truly meets your needs for a comfortable stay...",
      excerpt:
        "A comprehensive guide to finding and booking truly accessible accommodations for your travels.",
      author: "Emma Rodriguez",
      slug: "accessible-hotels-booking-guide",
      image:
        "https://images.pexels.com/photos/1579739/pexels-photo-1579739.jpeg?auto=compress&cs=tinysrgb&w=800",
      language: "en",
      published: true,
    },
  ];

  samplePosts.forEach((post) => {
    try {
      dbInstance.execute({
        sql: `
        INSERT OR IGNORE INTO blog_posts (title, content, excerpt, author, slug, image, language, published)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `,
        args: [
          post.title,
          post.content,
          post.excerpt,
          post.author,
          post.slug,
          post.image,
          post.language,
          post.published,
        ],
      });
    } catch (error) {
      console.log("Sample post already exists or error inserting:", error);
    }
  });
}
