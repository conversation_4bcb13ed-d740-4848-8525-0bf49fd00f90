import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";

export const dynamic = "force-dynamic";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export async function POST(request: NextRequest) {
  try {
    console.log("🔐 API: Login request received");
    const body = await request.json();
    console.log("🔐 API: Request body:", body);

    const { email, password } = body;

    if (!email || !password) {
      console.log("🔐 API: Missing email or password");
      return NextResponse.json(
        { message: "Email and password are required" },
        { status: 400 }
      );
    }

    // Find customer by email
    const result = await query("SELECT * FROM customers WHERE email = $1", [
      email,
    ]);

    if (result.rows.length === 0) {
      return NextResponse.json(
        { message: "Invalid email or password" },
        { status: 401 }
      );
    }

    const customer = result.rows[0];

    // Check password
    console.log(`🔐 API: Comparing password for ${email}`);
    console.log(
      `🔐 API: Stored hash: ${customer.password_hash.substring(0, 20)}...`
    );

    const isValidPassword = await bcrypt.compare(
      password,
      customer.password_hash
    );
    console.log(`🔐 API: Password valid: ${isValidPassword}`);

    if (!isValidPassword) {
      console.log(`🔐 API: Login failed - invalid password for ${email}`);
      return NextResponse.json(
        { message: "Invalid email or password" },
        { status: 401 }
      );
    }

    // Generate JWT token
    const token = jwt.sign(
      { customerId: customer.id, email: customer.email },
      JWT_SECRET,
      { expiresIn: "7d" }
    );

    // Remove password hash from response
    const { password_hash, ...customerData } = customer;

    const response = {
      token,
      customer: customerData,
      message: "Login successful",
    };

    console.log("🔐 API: Sending successful response:", response);
    return NextResponse.json(response);
  } catch (error) {
    console.error("🔐 API: Customer login error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
