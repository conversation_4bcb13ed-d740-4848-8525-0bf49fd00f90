import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import jwt from "jsonwebtoken";

export const dynamic = "force-dynamic";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    return jwt.verify(token, JWT_SECRET) as { customerId: number; email: string };
  } catch (error) {
    return null;
  }
}

// Process payment
export async function POST(request: NextRequest) {
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const {
      bookingId,
      paymentMethod,
      paymentProvider,
      amount,
    } = await request.json();

    // Verify booking belongs to customer and is payable
    const bookingResult = await query(
      `SELECT * FROM bookings 
       WHERE id = $1 AND customer_id = $2 AND status IN ('pending', 'confirmed') AND payment_status = 'unpaid'`,
      [bookingId, decoded.customerId]
    );

    if (bookingResult.rows.length === 0) {
      return NextResponse.json(
        { message: "Booking not found or not payable" },
        { status: 404 }
      );
    }

    const booking = bookingResult.rows[0];

    // Simulate payment processing
    const paymentId = `pay_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    // In a real app, you would integrate with Stripe, PayPal, etc.
    // For demo purposes, we'll simulate a successful payment
    const paymentSuccess = Math.random() > 0.1; // 90% success rate

    if (!paymentSuccess) {
      return NextResponse.json(
        { message: "Payment failed. Please try again." },
        { status: 400 }
      );
    }

    // Create payment record
    const paymentResult = await query(
      `INSERT INTO payments (booking_id, customer_id, amount, currency, payment_method, payment_provider, provider_payment_id, status)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8)
       RETURNING *`,
      [
        bookingId,
        decoded.customerId,
        amount,
        "USD",
        paymentMethod,
        paymentProvider,
        paymentId,
        "completed"
      ]
    );

    // Update booking payment status
    await query(
      `UPDATE bookings 
       SET payment_status = 'paid', payment_method = $1, payment_id = $2, status = 'confirmed', updated_at = CURRENT_TIMESTAMP
       WHERE id = $3`,
      [paymentMethod, paymentId, bookingId]
    );

    // Create notification
    await query(
      `INSERT INTO notifications (customer_id, booking_id, type, title, message)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        decoded.customerId,
        bookingId,
        "payment_completed",
        "Payment Successful",
        `Payment of $${amount} completed successfully for your booking to ${booking.destination}`
      ]
    );

    return NextResponse.json({
      payment: paymentResult.rows[0],
      message: "Payment processed successfully",
    });
  } catch (error) {
    console.error("Process payment error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Get customer's payment history
export async function GET(request: NextRequest) {
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json(
        { message: "Unauthorized" },
        { status: 401 }
      );
    }

    const result = await query(
      `SELECT p.*, b.destination, b.start_date, b.end_date
       FROM payments p
       JOIN bookings b ON p.booking_id = b.id
       WHERE p.customer_id = $1
       ORDER BY p.created_at DESC`,
      [decoded.customerId]
    );

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Get payment history error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
