import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/db";

export async function GET() {
  try {
    console.log("🔧 GET /api/settings - Fetching settings");

    // Get all settings from database
    const result = await query(
      `SELECT * FROM admin_settings ORDER BY id DESC LIMIT 1`
    );

    if (result.rows.length === 0) {
      // Return default settings if none exist
      const defaultSettings = {
        general: {
          siteName: "Freedom to Travel",
          adminEmail: "<EMAIL>",
          timezone: "UTC",
          currency: "NOK",
          language: "en",
          dateFormat: "DD/MM/YYYY",
          timeFormat: "24h",
          emailNotifications: true,
          bookingNotifications: true,
          blogNotifications: false,
          autoBackup: true,
          maintenanceMode: false,
        },
        security: {
          twoFactorAuth: false,
          sessionTimeout: 30,
          passwordExpiry: 90,
          loginAttempts: 5,
          requireStrongPassword: true,
          ipWhitelist: "",
          apiRateLimit: 1000,
        },
        appearance: {
          logo: "",
          favicon: "",
          darkMode: false,
          customCSS: "",
          footerText: "© 2024 Freedom to Travel",
          socialLinks: {
            facebook: "",
            instagram: "",
            twitter: "",
            linkedin: "",
          },
        },
        payment: {
          stripePublicKey: "",
          stripeSecretKey: "",
          paypalClientId: "",
          paypalClientSecret: "",
          currency: "USD",
          taxRate: 0,
          paymentMethods: {
            stripe: true,
            paypal: true,
            bankTransfer: false,
          },
          invoicePrefix: "INV-",
          invoiceTemplate: "default",
        },
        business: {
          companyName: "Freedom to Travel",
          address: "",
          phone: "",
          email: "",
          website: "",
          description: "",
          businessHours: {
            monday: { open: "09:00", close: "17:00", closed: false },
            tuesday: { open: "09:00", close: "17:00", closed: false },
            wednesday: { open: "09:00", close: "17:00", closed: false },
            thursday: { open: "09:00", close: "17:00", closed: false },
            friday: { open: "09:00", close: "17:00", closed: false },
            saturday: { open: "10:00", close: "16:00", closed: false },
            sunday: { open: "10:00", close: "16:00", closed: true },
          },
        },
      };

      console.log("🔧 No settings found, returning defaults");
      return NextResponse.json(defaultSettings);
    }

    const settings = result.rows[0];
    console.log("🔧 Settings found:", settings.id);

    // Parse JSON strings back to objects
    const parseJsonSafely = (jsonString: string) => {
      try {
        return jsonString ? JSON.parse(jsonString) : {};
      } catch (error) {
        console.error("Error parsing JSON:", error);
        return {};
      }
    };

    return NextResponse.json({
      general: parseJsonSafely(settings.general_settings),
      security: parseJsonSafely(settings.security_settings),
      appearance: parseJsonSafely(settings.appearance_settings),
      payment: parseJsonSafely(settings.payment_settings),
      business: parseJsonSafely(settings.business_settings),
    });
  } catch (error) {
    console.error("❌ Error fetching settings:", error);
    return NextResponse.json(
      { error: "Failed to fetch settings" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    console.log("🔧 POST /api/settings - Saving settings");

    const body = await request.json();
    const { general, security, appearance, payment, business } = body;

    console.log("🔧 Settings data received:", {
      general: !!general,
      security: !!security,
      appearance: !!appearance,
      payment: !!payment,
      business: !!business,
    });

    // Check if settings table exists, create if not
    await query(`
      CREATE TABLE IF NOT EXISTS admin_settings (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        general_settings TEXT,
        security_settings TEXT,
        appearance_settings TEXT,
        payment_settings TEXT,
        business_settings TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `);

    // Check if settings exist
    const existingResult = await query(
      `SELECT id FROM admin_settings ORDER BY id DESC LIMIT 1`
    );

    let result;
    if (existingResult.rows.length > 0) {
      // Update existing settings
      result = await query(
        `UPDATE admin_settings
         SET general_settings = ?,
             security_settings = ?,
             appearance_settings = ?,
             payment_settings = ?,
             business_settings = ?,
             updated_at = CURRENT_TIMESTAMP
         WHERE id = ?`,
        [
          JSON.stringify(general),
          JSON.stringify(security),
          JSON.stringify(appearance),
          JSON.stringify(payment),
          JSON.stringify(business),
          existingResult.rows[0].id,
        ]
      );

      // Get the updated record
      const updatedResult = await query(
        `SELECT * FROM admin_settings WHERE id = ?`,
        [existingResult.rows[0].id]
      );
      result = updatedResult;
    } else {
      // Insert new settings
      result = await query(
        `INSERT INTO admin_settings
         (general_settings, security_settings, appearance_settings, payment_settings, business_settings)
         VALUES (?, ?, ?, ?, ?)`,
        [
          JSON.stringify(general),
          JSON.stringify(security),
          JSON.stringify(appearance),
          JSON.stringify(payment),
          JSON.stringify(business),
        ]
      );

      // Get the inserted record
      const insertedResult = await query(
        `SELECT * FROM admin_settings WHERE id = ?`,
        [result.lastInsertRowid]
      );
      result = insertedResult;
    }

    console.log("🔧 Settings saved successfully:", result.rows[0]?.id);

    return NextResponse.json({
      success: true,
      message: "Settings saved successfully",
      settings: result.rows[0] || {},
    });
  } catch (error) {
    console.error("❌ Error saving settings:", error);
    return NextResponse.json(
      { error: "Failed to save settings" },
      { status: 500 }
    );
  }
}
