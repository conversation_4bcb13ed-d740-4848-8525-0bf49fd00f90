import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import bcrypt from "bcryptjs";

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const { token, password } = await request.json();

    if (!token || !password) {
      return NextResponse.json(
        { message: "Token and password are required" },
        { status: 400 }
      );
    }

    if (password.length < 6) {
      return NextResponse.json(
        { message: "Password must be at least 6 characters long" },
        { status: 400 }
      );
    }

    // Find customer with valid reset token
    const customerResult = await query(
      `SELECT id, name, email, reset_token_expires
       FROM customers
       WHERE reset_token = $1 AND reset_token_expires > CURRENT_TIMESTAMP`,
      [token]
    );

    if (customerResult.rows.length === 0) {
      return NextResponse.json(
        { message: "Invalid or expired reset token" },
        { status: 400 }
      );
    }

    const customer = customerResult.rows[0];

    // Hash new password
    const saltRounds = 10;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    console.log(`🔑 RESET: Hashing password for ${customer.email}`);
    console.log(`🔑 RESET: New hash: ${passwordHash.substring(0, 20)}...`);

    // Update password and clear reset token
    const updateResult = await query(
      `UPDATE customers
       SET password_hash = $1, reset_token = NULL, reset_token_expires = NULL, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2`,
      [passwordHash, customer.id]
    );

    console.log(`🔑 RESET: Update result:`, updateResult.rowCount);
    console.log(`🔑 PASSWORD RESET SUCCESSFUL for ${customer.email}`);

    return NextResponse.json({
      message:
        "Password reset successful. You can now login with your new password.",
    });
  } catch (error) {
    console.error("Reset password error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
