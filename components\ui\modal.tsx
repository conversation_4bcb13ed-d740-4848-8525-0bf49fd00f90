"use client";

import { useEffect } from "react";
import { X } from "lucide-react";
import { Button } from "./button";

interface ModalProps {
  isOpen: boolean;
  onClose: () => void;
  title: string;
  children: React.ReactNode;
  size?: "sm" | "md" | "lg" | "xl";
}

export function Modal({
  isOpen,
  onClose,
  title,
  children,
  size = "md",
}: ModalProps) {
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscape);
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleEscape);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const sizeClasses = {
    sm: "max-w-[300px] sm:max-w-sm md:max-w-md",
    md: "max-w-[320px] sm:max-w-md md:max-w-lg",
    lg: "max-w-[340px] sm:max-w-lg md:max-w-xl lg:max-w-2xl xl:max-w-3xl",
    xl: "max-w-[360px] sm:max-w-xl md:max-w-2xl lg:max-w-3xl xl:max-w-4xl 2xl:max-w-5xl",
  };

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center p-1 sm:p-2 md:p-4 modal-small-screen">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black bg-opacity-50 transition-opacity"
        onClick={onClose}
      />

      {/* Modal */}
      <div
        className={`relative bg-white rounded-lg shadow-xl w-[98vw] ${sizeClasses[size]} max-h-[95vh] sm:max-h-[90vh] md:max-h-[85vh] lg:max-h-[80vh] overflow-hidden flex flex-col ultra-small-modal`}
      >
        {/* Header */}
        <div className="flex items-center justify-between p-3 sm:p-4 md:p-5 lg:p-6 border-b border-slate-200 flex-shrink-0 ultra-small-modal-header">
          <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold text-gray-900 truncate ultra-small-modal-title">
            {title}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 h-6 w-6 sm:h-8 sm:w-8 md:h-9 md:w-9 flex-shrink-0 ml-2 ultra-small-modal-close"
          >
            <X className="h-3 w-3 sm:h-4 sm:w-4 md:h-5 md:w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-3 sm:p-4 md:p-5 lg:p-6 overflow-y-auto flex-1 min-h-0 ultra-small-modal-content">
          {children}
        </div>
      </div>
    </div>
  );
}
