"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Calendar, Users, MapPin } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import { toast } from "sonner";

export default function BookingSection() {
  const { t } = useLanguage();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    phone: "",
    destination: "",
    startDate: "",
    endDate: "",
    travelers: "",
    requirements: "",
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const response = await fetch("/api/bookings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      if (response.ok) {
        toast.success(
          "Booking request submitted successfully! We'll contact you soon."
        );
        setFormData({
          name: "",
          email: "",
          phone: "",
          destination: "",
          startDate: "",
          endDate: "",
          travelers: "",
          requirements: "",
        });
      } else {
        throw new Error("Failed to submit booking");
      }
    } catch (error) {
      toast.error("Failed to submit booking request. Please try again.");
      console.error("Booking submission error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <section id="booking" className="py-20 px-4 sm:px-6 lg:px-8 bg-white">
      <div className="max-w-4xl mx-auto">
        <Card className="shadow-2xl">
          <CardHeader className="text-center">
            <CardTitle className="text-3xl sm:text-4xl font-bold text-gray-900">
              {t("booking.title")}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <Label htmlFor="name" className="font-semibold text-gray-700">
                    {t("booking.form.name")}
                  </Label>
                  <Input
                    id="name"
                    name="name"
                    type="text"
                    value={formData.name}
                    onChange={handleInputChange}
                    required
                    className="mt-2"
                    placeholder=""
                  />
                </div>
                <div>
                  <Label
                    htmlFor="email"
                    className="font-semibold text-gray-700"
                  >
                    {t("booking.form.email")}
                  </Label>
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                    className="mt-2"
                    placeholder=""
                  />
                </div>
              </div>

              <div>
                <Label htmlFor="phone" className="font-semibold text-gray-700">
                  {t("booking.form.phone")}
                </Label>
                <Input
                  id="phone"
                  name="phone"
                  type="tel"
                  value={formData.phone}
                  onChange={handleInputChange}
                  className="mt-2"
                  placeholder=""
                />
              </div>

              <div>
                <Label
                  htmlFor="destination"
                  className="font-semibold text-gray-700"
                >
                  {t("booking.form.destination")}
                </Label>
                <Input
                  id="destination"
                  name="destination"
                  type="text"
                  value={formData.destination}
                  onChange={handleInputChange}
                  required
                  className="mt-2"
                  placeholder=""
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                <div>
                  <Label
                    htmlFor="startDate"
                    className="font-semibold text-gray-700"
                  >
                    {t("booking.form.startDate")}
                  </Label>
                  <Input
                    id="startDate"
                    name="startDate"
                    type="date"
                    value={formData.startDate}
                    onChange={handleInputChange}
                    required
                    className="mt-2"
                  />
                </div>
                <div>
                  <Label
                    htmlFor="endDate"
                    className="font-semibold text-gray-700"
                  >
                    {t("booking.form.endDate")}
                  </Label>
                  <Input
                    id="endDate"
                    name="endDate"
                    type="date"
                    value={formData.endDate}
                    onChange={handleInputChange}
                    required
                    className="mt-2"
                  />
                </div>
              </div>

              <div>
                <Label
                  htmlFor="travelers"
                  className="font-semibold text-gray-700"
                >
                  {t("booking.form.travelers")}
                </Label>
                <Input
                  id="travelers"
                  name="travelers"
                  type="number"
                  min="1"
                  value={formData.travelers}
                  onChange={handleInputChange}
                  required
                  className="mt-2"
                  placeholder=""
                />
              </div>

              <div>
                <Label
                  htmlFor="requirements"
                  className="font-semibold text-gray-700"
                >
                  {t("booking.form.requirements")}
                </Label>
                <Textarea
                  id="requirements"
                  name="requirements"
                  value={formData.requirements}
                  onChange={handleInputChange}
                  className="mt-2"
                  rows={4}
                  placeholder={t("")}
                />
              </div>

              <Button
                type="submit"
                className="w-full text-white transition-colors duration-300 py-3 text-lg font-bold rounded-md"
                style={{
                  backgroundColor: "#f00d45",
                }}
                onMouseEnter={(e) => {
                  if (!isSubmitting)
                    e.currentTarget.style.backgroundColor = "#d00b3a";
                }}
                onMouseLeave={(e) => {
                  if (!isSubmitting)
                    e.currentTarget.style.backgroundColor = "#f00d45";
                }}
                disabled={isSubmitting}
              >
                {isSubmitting ? "Submitting..." : t("booking.form.submit")}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>
    </section>
  );
}
