"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useCustomerAuth } from "@/contexts/CustomerAuthContext";
import { useCenterModal } from "@/hooks/useCenterModal";
import {
  Calendar,
  MapPin,
  DollarSign,
  Clock,
  CheckCircle,
  XCircle,
  Plus,
  CreditCard,
  User,
  LogOut,
  Home,
  ArrowLeft,
  Settings,
  Bell,
  Mail,
  Phone,
  Eye,
  Edit,
  Trash2,
} from "lucide-react";
import { toast } from "sonner";

interface Booking {
  id: number;
  destination: string;
  start_date: string;
  end_date: string;
  travelers: number;
  price: number;
  status: string;
  payment_status: string;
  created_at: string;
}

interface Payment {
  id: number;
  booking_id: number;
  amount: number;
  currency: string;
  payment_method: string;
  status: string;
  created_at: string;
  destination: string;
}

export default function CustomerDashboard() {
  const router = useRouter();
  const { customer, logout, isLoading } = useCustomerAuth();
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loadingBookings, setLoadingBookings] = useState(true);
  const [loadingPayments, setLoadingPayments] = useState(true);

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [localCustomer, setLocalCustomer] = useState<any>(null);
  const [currentView, setCurrentView] = useState("dashboard");
  const [showBookingModal, setShowBookingModal] = useState(false);
  const [bookingForm, setBookingForm] = useState({
    destination: "",
    start_date: "",
    end_date: "",
    travelers: 1,
    budget: "",
    notes: "",
  });

  // Settings modals
  const [showEditProfileModal, setShowEditProfileModal] = useState(false);
  const [showChangePasswordModal, setShowChangePasswordModal] = useState(false);
  const [showNotificationModal, setShowNotificationModal] = useState(false);
  const [showEmailPreferencesModal, setShowEmailPreferencesModal] =
    useState(false);
  const [showDeleteAccountModal, setShowDeleteAccountModal] = useState(false);

  // Edit profile form
  const [editProfileData, setEditProfileData] = useState({
    name: "",
    email: "",
    phone: "",
  });

  // Change password form
  const [passwordData, setPasswordData] = useState({
    currentPassword: "",
    newPassword: "",
    confirmPassword: "",
  });

  useEffect(() => {
    // Check if token exists in localStorage first
    const token = localStorage.getItem("customer_token");
    if (!token) {
      router.push("/customer/auth");
      return;
    }

    // Fetch customer data directly if context is not loaded
    const fetchCustomerData = async () => {
      try {
        const response = await fetch("/api/customer/profile", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const contentType = response.headers.get("content-type");
          if (contentType && contentType.includes("application/json")) {
            const customerData = await response.json();
            setLocalCustomer(customerData);
            fetchBookings();
            fetchPayments();
          } else {
            console.error("Expected JSON but received:", contentType);
            router.push("/customer/auth");
          }
        } else {
          console.error(
            "Response not ok:",
            response.status,
            response.statusText
          );
          router.push("/customer/auth");
        }
      } catch (error) {
        console.error("Error fetching customer data:", error);
        router.push("/customer/auth");
      }
    };

    if (customer) {
      setLocalCustomer(customer);
      fetchBookings();
      fetchPayments();
    } else if (!isLoading) {
      fetchCustomerData();
    }
  }, [customer, isLoading, router]);

  // Initialize edit profile data when customer is loaded
  useEffect(() => {
    if (localCustomer) {
      setEditProfileData({
        name: localCustomer.name || "",
        email: localCustomer.email || "",
        phone: localCustomer.phone || "",
      });
    }
  }, [localCustomer]);

  const fetchBookings = async () => {
    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/customer/bookings", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const data = await response.json();
          setBookings(data);
        } else {
          console.error("Expected JSON but received:", contentType);
          toast.error("Failed to fetch bookings - invalid response format");
        }
      } else {
        console.error(
          "Failed to fetch bookings:",
          response.status,
          response.statusText
        );
        toast.error("Failed to fetch bookings");
      }
    } catch (error) {
      console.error("Error fetching bookings:", error);
      toast.error("Failed to fetch bookings");
    } finally {
      setLoadingBookings(false);
    }
  };

  const fetchPayments = async () => {
    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/customer/payments", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const contentType = response.headers.get("content-type");
        if (contentType && contentType.includes("application/json")) {
          const data = await response.json();
          setPayments(data);
        } else {
          console.error("Expected JSON but received:", contentType);
          toast.error("Failed to fetch payments - invalid response format");
        }
      } else {
        console.error(
          "Failed to fetch payments:",
          response.status,
          response.statusText
        );
        toast.error("Failed to fetch payments");
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      toast.error("Failed to fetch payments");
    } finally {
      setLoadingPayments(false);
    }
  };

  const goToHome = () => {
    router.push("/");
  };

  const handleLogout = () => {
    logout();
    router.push("/customer/auth");
  };

  const navigateToTab = (tab: string) => {
    setCurrentView(tab);
  };

  // Settings functions
  const handleEditProfile = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/customer/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(editProfileData),
      });

      if (response.ok) {
        const updatedCustomer = await response.json();
        setLocalCustomer(updatedCustomer);
        setShowEditProfileModal(false);
        toast.success("Profile updated successfully!");
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to update profile");
      }
    } catch (error) {
      console.error("Error updating profile:", error);
      toast.error("Failed to update profile");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleChangePassword = async (e: React.FormEvent) => {
    e.preventDefault();

    if (passwordData.newPassword !== passwordData.confirmPassword) {
      toast.error("New passwords don't match");
      return;
    }

    if (passwordData.newPassword.length < 6) {
      toast.error("Password must be at least 6 characters");
      return;
    }

    setIsSubmitting(true);

    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/customer/change-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          currentPassword: passwordData.currentPassword,
          newPassword: passwordData.newPassword,
        }),
      });

      if (response.ok) {
        setShowChangePasswordModal(false);
        setPasswordData({
          currentPassword: "",
          newPassword: "",
          confirmPassword: "",
        });
        toast.success("Password changed successfully!");
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to change password");
      }
    } catch (error) {
      console.error("Error changing password:", error);
      toast.error("Failed to change password");
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteAccount = async () => {
    setIsSubmitting(true);

    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/customer/profile", {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        localStorage.removeItem("customer_token");
        document.cookie =
          "customer_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
        toast.success("Account deleted successfully");
        router.push("/customer/auth");
      } else {
        const error = await response.json();
        toast.error(error.message || "Failed to delete account");
      }
    } catch (error) {
      console.error("Error deleting account:", error);
      toast.error("Failed to delete account");
    } finally {
      setIsSubmitting(false);
      setShowDeleteAccountModal(false);
    }
  };

  const handleCreateBooking = (e?: React.MouseEvent) => {
    e?.preventDefault();
    e?.stopPropagation();
    setShowBookingModal(true);
  };

  const handleViewBooking = (booking: Booking) => {
    // For now, just show an alert with booking details
    alert(
      `Booking Details:\nID: ${booking.id}\nDestination: ${booking.destination}\nStatus: ${booking.status}\nAmount: $${booking.price}`
    );
  };

  const handlePayNow = (booking: Booking) => {
    // Redirect to payments page with booking ID
    router.push(`/customer/payments?booking_id=${booking.id}`);
  };

  const handleBookingSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/bookings", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({
          ...bookingForm,
          price: parseFloat(bookingForm.budget) || 0,
        }),
      });

      if (response.ok) {
        toast.success("Booking created successfully!");
        setShowBookingModal(false);
        setBookingForm({
          destination: "",
          start_date: "",
          end_date: "",
          travelers: 1,
          budget: "",
          notes: "",
        });
        // Refresh bookings
        fetchBookings();
      } else {
        toast.error("Failed to create booking");
      }
    } catch (error) {
      console.error("Error creating booking:", error);
      toast.error("Failed to create booking");
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
    }).format(amount);
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "confirmed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "cancelled":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getPaymentStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "paid":
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  // Loading state
  if (isLoading || !localCustomer) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="text-center">
          <div
            className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4"
            style={{ borderColor: "#f00d45" }}
          ></div>
          <p className="text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  // Main component render
  return (
    <div className="h-screen bg-gray-50 flex flex-col overflow-hidden ultra-small-screen">
      {/* Navbar - Fixed and responsive */}
      <div className="bg-white border-b border-slate-200 sticky top-0 z-50 shadow-sm">
        <div className="max-w-7xl mx-auto px-2 sm:px-4 lg:px-8">
          <div className="flex items-center justify-between h-10 sm:h-12 md:h-16 lg:h-20 ultra-small-navbar">
            {/* Logo - Responsive sizing */}
            <div className="flex items-center min-w-0 flex-1">
              <h1
                className="text-xs sm:text-sm md:text-lg lg:text-xl font-bold truncate ultra-small-title"
                style={{ color: "#f00d45" }}
              >
                Freedom to Travel
              </h1>
            </div>

            {/* Navigation Buttons - Responsive */}
            <div className="flex items-center gap-1 sm:gap-2 md:gap-3 flex-shrink-0">
              {/* Mobile Menu Buttons */}
              <div className="sm:hidden flex items-center gap-1">
                <Button
                  onClick={goToHome}
                  variant="outline"
                  size="sm"
                  className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white p-1 h-7 w-7 ultra-small-logout"
                >
                  <Home className="h-3 w-3" />
                </Button>
                <Button
                  onClick={handleLogout}
                  variant="outline"
                  size="sm"
                  className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white p-1 h-7 w-7 ultra-small-logout"
                >
                  <LogOut className="h-3 w-3" />
                </Button>
              </div>

              {/* Desktop Navigation */}
              <div className="hidden sm:flex items-center gap-2 md:gap-3">
                <Button
                  onClick={goToHome}
                  variant="outline"
                  size="sm"
                  className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white text-xs md:text-sm"
                >
                  <Home className="h-4 w-4 mr-1 md:mr-2" />
                  <span className="hidden md:inline">Go to Site</span>
                  <span className="md:hidden">Site</span>
                </Button>
                <Button
                  onClick={handleLogout}
                  variant="outline"
                  size="sm"
                  className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white text-xs md:text-sm"
                >
                  <LogOut className="h-4 w-4 mr-1 md:mr-2" />
                  <span className="hidden md:inline">Logout</span>
                  <span className="md:hidden">Exit</span>
                </Button>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col overflow-hidden">
        <div className="max-w-7xl mx-auto w-full px-1 sm:px-2 md:px-4 lg:px-8 py-1 sm:py-2 md:py-4 lg:py-8 flex-1 flex flex-col min-h-0 ultra-small-main ultra-small-no-overflow">
          {currentView === "dashboard" ? (
            <div className="flex flex-col justify-center items-center min-h-full flex-1 ultra-small-dashboard">
              {/* Dashboard Overview */}
              {/* Welcome Section - Responsive */}
              <div className="text-center mb-3 sm:mb-4 md:mb-6 lg:mb-8">
                <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl font-bold text-gray-900 mb-1 ultra-small-title">
                  Welcome back, {localCustomer.name}!
                </h2>
                <p className="text-gray-600 text-xs sm:text-sm md:text-base max-w-2xl mx-auto ultra-small-subtitle hide-on-ultra-small">
                  Manage your travel bookings and payments from your personal
                  dashboard
                </p>
              </div>

              {/* Dashboard Cards - Responsive grid with 2 cards per row on mobile, 2x2 on tablet, 4 on desktop */}
              <div className="flex justify-center items-center w-full px-1 sm:px-2 md:px-4 lg:px-8">
                <div className="grid grid-cols-2 md:grid-cols-2 lg:grid-cols-4 gap-2 sm:gap-3 md:gap-4 lg:gap-6 w-full max-w-6xl mx-auto ultra-small-grid">
                  {/* My Bookings Card */}
                  <Card
                    className="bg-gradient-to-br from-blue-500 to-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group transform hover:scale-105 aspect-square md:aspect-[3/2] lg:aspect-square ultra-small-card"
                    onClick={() => navigateToTab("bookings")}
                  >
                    <CardContent className="p-1 sm:p-2 md:p-3 lg:p-4 h-full flex flex-col items-center justify-center text-center">
                      {/* Simplified version for ultra small screens */}
                      <div className="show-on-ultra-small">
                        <Calendar className="h-6 w-6 text-blue-200 mx-auto mb-1" />
                        <div className="text-xs font-medium text-blue-100">
                          Bookings
                        </div>
                        <div className="text-sm font-bold">
                          {bookings.length}
                        </div>
                      </div>

                      {/* Full version for larger screens */}
                      <div className="hide-on-ultra-small">
                        <Calendar className="h-3 w-3 sm:h-4 sm:w-4 md:h-6 md:w-6 lg:h-8 lg:w-8 text-blue-200 group-hover:text-blue-100 transition-colors mx-auto ultra-small-icon mb-1" />
                        <div className="text-xs sm:text-sm font-medium text-blue-100 mb-0.5">
                          My Bookings
                        </div>
                        <div className="text-sm sm:text-base md:text-lg lg:text-2xl font-bold ultra-small-number">
                          {bookings.length}
                        </div>
                        <div className="text-xs text-blue-100">
                          {
                            bookings.filter((b) => b.status === "confirmed")
                              .length
                          }{" "}
                          confirmed
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Payment History Card */}
                  <Card
                    className="bg-gradient-to-br from-green-500 to-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group transform hover:scale-105 aspect-square md:aspect-[3/2] lg:aspect-square ultra-small-card"
                    onClick={() => navigateToTab("payments")}
                  >
                    <CardContent className="p-1 sm:p-2 md:p-3 lg:p-4 h-full flex flex-col items-center justify-center text-center">
                      {/* Simplified version for ultra small screens */}
                      <div className="show-on-ultra-small">
                        <DollarSign className="h-6 w-6 text-green-200 mx-auto mb-1" />
                        <div className="text-xs font-medium text-green-100">
                          Spent
                        </div>
                        <div className="text-xs font-bold">
                          {formatCurrency(
                            payments.reduce((sum, p) => sum + p.amount, 0)
                          )}
                        </div>
                      </div>

                      {/* Full version for larger screens */}
                      <div className="hide-on-ultra-small">
                        <div className="mb-0.5 sm:mb-1 md:mb-2 lg:mb-3">
                          <CreditCard className="h-3 w-3 sm:h-4 sm:w-4 md:h-6 md:w-6 lg:h-8 lg:w-8 text-green-200 group-hover:text-green-100 transition-colors mx-auto ultra-small-icon" />
                        </div>
                        <div>
                          <p className="text-green-100 text-xs font-medium mb-0.5">
                            Total Spent
                          </p>
                          <p className="text-xs sm:text-sm md:text-base lg:text-lg font-bold mb-0.5 ultra-small-number">
                            {formatCurrency(
                              payments.reduce((sum, p) => sum + p.amount, 0)
                            )}
                          </p>
                          <p className="text-xs text-green-100">
                            {payments.length} payments
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Settings Card */}
                  <Card
                    className="bg-gradient-to-br from-purple-500 to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group transform hover:scale-105 aspect-square md:aspect-[3/2] lg:aspect-square ultra-small-card"
                    onClick={() => navigateToTab("settings")}
                  >
                    <CardContent className="p-1 sm:p-2 md:p-3 lg:p-4 h-full flex flex-col items-center justify-center text-center">
                      {/* Simplified version for ultra small screens */}
                      <div className="show-on-ultra-small">
                        <Settings className="h-6 w-6 text-purple-200 mx-auto mb-1" />
                        <div className="text-xs font-medium text-purple-100">
                          Settings
                        </div>
                        <div className="text-xs font-bold">Profile</div>
                      </div>

                      {/* Full version for larger screens */}
                      <div className="hide-on-ultra-small">
                        <div className="mb-0.5 sm:mb-1 md:mb-2 lg:mb-3">
                          <Settings className="h-3 w-3 sm:h-4 sm:w-4 md:h-6 md:w-6 lg:h-8 lg:w-8 text-purple-200 group-hover:text-purple-100 transition-colors mx-auto ultra-small-icon" />
                        </div>
                        <div>
                          <p className="text-purple-100 text-xs sm:text-sm font-medium mb-0.5 sm:mb-1">
                            Account Settings
                          </p>
                          <p className="text-lg sm:text-xl md:text-2xl lg:text-4xl font-bold mb-0.5 sm:mb-1 ultra-small-number">
                            Profile
                          </p>
                          <p className="text-xs sm:text-sm text-purple-100">
                            Manage account
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Quick Actions Card */}
                  <Card
                    className="bg-gradient-to-br from-orange-500 to-orange-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group transform hover:scale-105 aspect-square md:aspect-[3/2] lg:aspect-square ultra-small-card"
                    onClick={(e) => handleCreateBooking(e)}
                  >
                    <CardContent className="p-1 sm:p-2 md:p-3 lg:p-4 h-full flex flex-col items-center justify-center text-center">
                      {/* Simplified version for ultra small screens */}
                      <div className="show-on-ultra-small">
                        <Plus className="h-6 w-6 text-orange-200 mx-auto mb-1" />
                        <div className="text-xs font-medium text-orange-100">
                          Book
                        </div>
                        <div className="text-xs font-bold">Trip</div>
                      </div>

                      {/* Full version for larger screens */}
                      <div className="hide-on-ultra-small">
                        <div className="mb-1 sm:mb-2 md:mb-3 lg:mb-4">
                          <Plus className="h-4 w-4 sm:h-6 sm:w-6 md:h-8 md:w-8 lg:h-12 lg:w-12 text-orange-200 group-hover:text-orange-100 transition-colors mx-auto ultra-small-icon" />
                        </div>
                        <div>
                          <p className="text-orange-100 text-xs sm:text-sm font-medium mb-0.5 sm:mb-1">
                            Quick Actions
                          </p>
                          <p className="text-lg sm:text-xl md:text-2xl lg:text-4xl font-bold mb-0.5 sm:mb-1 ultra-small-number">
                            Book
                          </p>
                          <p className="text-xs sm:text-sm text-orange-100">
                            New trip
                          </p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex flex-col flex-1 min-h-0">
              {/* Tab Views */}
              {/* Mobile & Tablet Tabs - Same style as Admin Dashboard */}
              <div className="lg:hidden flex-shrink-0">
                <div className="mb-2 sm:mb-4">
                  <div className="grid w-full grid-cols-4 bg-white border border-slate-200 rounded-lg h-10 sm:h-12 p-0.5 sm:p-1 shadow-sm ultra-small-tabs">
                    <Button
                      onClick={() => setCurrentView("dashboard")}
                      className="bg-transparent hover:bg-blue-600 hover:text-white text-blue-600 border-0 shadow-none flex flex-col items-center justify-center gap-0.5 sm:gap-1 px-0.5 sm:px-1 h-full rounded-md transition-all duration-200"
                    >
                      <Home className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                      <span className="hidden sm:inline text-xs">
                        Dashboard
                      </span>
                    </Button>
                    <Button
                      onClick={() => navigateToTab("bookings")}
                      className={`border-0 shadow-none text-xs font-medium transition-all duration-200 flex flex-col items-center justify-center gap-0.5 sm:gap-1 px-0.5 sm:px-1 h-full rounded-md ${
                        currentView === "bookings"
                          ? "bg-blue-600 text-white"
                          : "bg-transparent text-gray-600 hover:bg-blue-600 hover:text-white"
                      }`}
                    >
                      <Calendar className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                      <span className="hidden sm:inline text-xs">Bookings</span>
                    </Button>
                    <Button
                      onClick={() => navigateToTab("payments")}
                      className={`border-0 shadow-none text-xs font-medium transition-all duration-200 flex flex-col items-center justify-center gap-0.5 sm:gap-1 px-0.5 sm:px-1 h-full rounded-md ${
                        currentView === "payments"
                          ? "bg-blue-600 text-white"
                          : "bg-transparent text-gray-600 hover:bg-blue-600 hover:text-white"
                      }`}
                    >
                      <CreditCard className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                      <span className="hidden sm:inline text-xs">Payments</span>
                    </Button>
                    <Button
                      onClick={() => navigateToTab("settings")}
                      className={`border-0 shadow-none text-xs font-medium transition-all duration-200 flex flex-col items-center justify-center gap-0.5 sm:gap-1 px-0.5 sm:px-1 h-full rounded-md ${
                        currentView === "settings"
                          ? "bg-blue-600 text-white"
                          : "bg-transparent text-gray-600 hover:bg-blue-600 hover:text-white"
                      }`}
                    >
                      <Settings className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                      <span className="hidden sm:inline text-xs">Settings</span>
                    </Button>
                  </div>
                </div>

                {/* Mobile & Tablet Content */}
                <div className="flex-1 px-2 sm:px-4 pb-2 sm:pb-4 min-h-0 flex flex-col">
                  {currentView === "bookings" && (
                    <div className="flex-1 flex flex-col min-h-0">
                      <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-2 sm:gap-3 mb-2 sm:mb-4">
                        <div>
                          <h3 className="text-base sm:text-lg font-semibold text-gray-900 ultra-small-title">
                            My Bookings
                          </h3>
                          <p className="text-xs sm:text-sm text-gray-600 ultra-small-subtitle">
                            {bookings.length} total bookings
                          </p>
                        </div>
                        <Button
                          onClick={(e) => handleCreateBooking(e)}
                          className="bg-blue-600 hover:bg-blue-700 text-white w-full sm:w-auto text-xs sm:text-sm"
                          size="sm"
                        >
                          <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ultra-small-icon" />
                          <span className="sm:hidden">New Booking</span>
                          <span className="hidden sm:inline">Add Booking</span>
                        </Button>
                      </div>

                      {/* Scrollable Content */}
                      <div className="flex-1 overflow-y-auto space-y-2 sm:space-y-4 pb-16 sm:pb-20 min-h-0">
                        {loadingBookings ? (
                          <div className="text-center py-4 sm:py-8">
                            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600 mx-auto mb-2 sm:mb-4"></div>
                            <p className="text-gray-600 text-sm sm:text-base">
                              Loading bookings...
                            </p>
                          </div>
                        ) : bookings.length === 0 ? (
                          <div className="text-center py-4 sm:py-8">
                            <Calendar className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2 sm:mb-4 ultra-small-icon" />
                            <p className="text-gray-600 mb-2 sm:mb-4 text-sm sm:text-base ultra-small-subtitle">
                              No bookings yet
                            </p>
                            <Button
                              onClick={(e) => handleCreateBooking(e)}
                              className="bg-blue-600 hover:bg-blue-700 text-white text-xs sm:text-sm"
                              size="sm"
                            >
                              <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ultra-small-icon" />
                              Create Your First Booking
                            </Button>
                          </div>
                        ) : (
                          <div className="space-y-2 sm:space-y-4">
                            {bookings.map((booking) => (
                              <Card
                                key={booking.id}
                                className="shadow-sm hover:shadow-md transition-shadow ultra-small-card"
                              >
                                <CardContent className="p-2 sm:p-3 md:p-4">
                                  <div className="flex justify-between items-start mb-2 sm:mb-3">
                                    <div>
                                      <h4 className="font-semibold text-gray-900 text-sm sm:text-base ultra-small-title">
                                        {booking.destination}
                                      </h4>
                                      <p className="text-xs sm:text-sm text-gray-600 ultra-small-subtitle">
                                        {formatDate(booking.start_date)} -{" "}
                                        {formatDate(booking.end_date)}
                                      </p>
                                    </div>
                                    <Badge
                                      className={getStatusColor(booking.status)}
                                    >
                                      {booking.status}
                                    </Badge>
                                  </div>

                                  <div className="flex justify-between items-center mb-2 sm:mb-3">
                                    <div className="flex items-center gap-1 sm:gap-2">
                                      <User className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                                      <span className="text-xs sm:text-sm">
                                        {booking.travelers} travelers
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-1 sm:gap-2">
                                      <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                                      <span className="text-xs sm:text-sm font-medium">
                                        {formatCurrency(booking.price)}
                                      </span>
                                    </div>
                                  </div>

                                  <div className="flex justify-between items-center">
                                    <Badge
                                      className={`${getPaymentStatusColor(
                                        booking.payment_status
                                      )} text-xs`}
                                    >
                                      {booking.payment_status}
                                    </Badge>
                                    <div className="flex gap-1 sm:gap-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleViewBooking(booking)
                                        }
                                        className="p-1 sm:p-2"
                                      >
                                        <Eye className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                                      </Button>
                                      {booking.payment_status !== "paid" && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => handlePayNow(booking)}
                                          className="p-1 sm:p-2"
                                        >
                                          <CreditCard className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                                        </Button>
                                      )}
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {currentView === "payments" && (
                    <div className="flex-1 flex flex-col min-h-0">
                      <div className="flex justify-between items-center mb-2 sm:mb-4 flex-shrink-0">
                        <h3 className="text-base sm:text-lg font-semibold ultra-small-title">
                          Payment History
                        </h3>
                        <div className="text-xs sm:text-sm text-gray-600 ultra-small-subtitle">
                          Total:{" "}
                          {formatCurrency(
                            payments.reduce((sum, p) => sum + p.amount, 0)
                          )}
                        </div>
                      </div>

                      {/* Scrollable Content */}
                      <div className="flex-1 overflow-y-auto space-y-2 sm:space-y-4 pb-16 sm:pb-20 min-h-0">
                        {loadingPayments ? (
                          <div className="text-center py-4 sm:py-8">
                            <div className="animate-spin rounded-full h-6 w-6 sm:h-8 sm:w-8 border-b-2 border-blue-600 mx-auto mb-2 sm:mb-4"></div>
                            <p className="text-gray-600 text-sm sm:text-base">
                              Loading payments...
                            </p>
                          </div>
                        ) : payments.length === 0 ? (
                          <div className="text-center py-4 sm:py-8">
                            <CreditCard className="h-8 w-8 sm:h-12 sm:w-12 text-gray-400 mx-auto mb-2 sm:mb-4 ultra-small-icon" />
                            <p className="text-gray-600 text-sm sm:text-base ultra-small-subtitle">
                              No payments yet
                            </p>
                          </div>
                        ) : (
                          <div className="space-y-2 sm:space-y-4">
                            {payments.map((payment) => (
                              <Card
                                key={payment.id}
                                className="shadow-sm hover:shadow-md transition-shadow ultra-small-card"
                              >
                                <CardContent className="p-2 sm:p-3 md:p-4">
                                  <div className="flex justify-between items-start mb-2 sm:mb-3">
                                    <div>
                                      <h4 className="font-semibold text-gray-900 text-sm sm:text-base ultra-small-title">
                                        {payment.destination}
                                      </h4>
                                      <p className="text-xs sm:text-sm text-gray-600 ultra-small-subtitle">
                                        {formatDate(payment.created_at)}
                                      </p>
                                    </div>
                                    <Badge
                                      className={`${getPaymentStatusColor(
                                        payment.status
                                      )} text-xs`}
                                    >
                                      {payment.status}
                                    </Badge>
                                  </div>

                                  <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-1 sm:gap-2">
                                      <DollarSign className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                                      <span className="text-xs sm:text-sm font-medium">
                                        {formatCurrency(payment.amount)}
                                      </span>
                                    </div>
                                    <div className="flex items-center gap-1 sm:gap-2">
                                      <CreditCard className="h-3 w-3 sm:h-4 sm:w-4 ultra-small-icon" />
                                      <span className="text-xs sm:text-sm">
                                        {payment.payment_method}
                                      </span>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        )}
                      </div>
                    </div>
                  )}

                  {currentView === "settings" && (
                    <div className="h-full flex flex-col ultra-small-settings">
                      <div className="flex justify-between items-center mb-1 sm:mb-2 flex-shrink-0">
                        <h3 className="text-sm sm:text-base font-semibold ultra-small-title">
                          Settings
                        </h3>
                      </div>

                      {/* Scrollable Content */}
                      <div
                        className="flex-1 overflow-y-scroll space-y-1 sm:space-y-2 pb-12 sm:pb-16 min-h-0"
                        style={{
                          height: "calc(100vh - 120px)",
                          maxHeight: "calc(100vh - 120px)",
                        }}
                      >
                        {/* Profile Settings */}
                        <Card className="shadow-sm">
                          <CardContent className="p-1.5 sm:p-2 md:p-3">
                            <h4 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-xs sm:text-sm ultra-small-title">
                              Profile Information
                            </h4>
                            <div className="space-y-1 sm:space-y-2">
                              <div>
                                <label className="text-xs font-medium text-gray-700">
                                  Name
                                </label>
                                <p className="text-xs text-gray-600 mt-0.5">
                                  {localCustomer.name}
                                </p>
                              </div>
                              <div>
                                <label className="text-xs font-medium text-gray-700">
                                  Email
                                </label>
                                <p className="text-xs text-gray-600 mt-0.5">
                                  {localCustomer.email}
                                </p>
                              </div>
                              <div>
                                <label className="text-xs font-medium text-gray-700">
                                  Phone
                                </label>
                                <p className="text-xs text-gray-600 mt-0.5">
                                  {localCustomer.phone || "Not provided"}
                                </p>
                              </div>
                              <Button
                                variant="outline"
                                className="w-full text-xs h-7"
                                size="sm"
                                onClick={() => setShowEditProfileModal(true)}
                              >
                                <Edit className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2 ultra-small-icon" />
                                Edit Profile
                              </Button>
                            </div>
                          </CardContent>
                        </Card>

                        {/* Account Settings */}
                        <Card className="shadow-sm">
                          <CardContent className="p-1.5 sm:p-2 md:p-3">
                            <h4 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-xs sm:text-sm ultra-small-title">
                              Account Settings
                            </h4>
                            <div className="space-y-1">
                              <Button
                                variant="outline"
                                className="w-full justify-start text-xs h-7"
                                size="sm"
                                onClick={() => setShowChangePasswordModal(true)}
                              >
                                <Settings className="h-3 w-3 mr-1 ultra-small-icon" />
                                Change Password
                              </Button>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-xs h-7"
                                size="sm"
                                onClick={() => setShowNotificationModal(true)}
                              >
                                <Bell className="h-3 w-3 mr-1 ultra-small-icon" />
                                Notifications
                              </Button>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-xs h-7"
                                size="sm"
                                onClick={() =>
                                  setShowEmailPreferencesModal(true)
                                }
                              >
                                <Mail className="h-3 w-3 mr-1 ultra-small-icon" />
                                Email Preferences
                              </Button>
                            </div>
                          </CardContent>
                        </Card>

                        {/* Account Status */}
                        <Card className="shadow-sm">
                          <CardContent className="p-1.5 sm:p-2 md:p-3">
                            <h4 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-xs sm:text-sm ultra-small-title">
                              Account Status
                            </h4>
                            <div className="space-y-1 sm:space-y-2">
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-700">
                                  Email Verified
                                </span>
                                <Badge
                                  className={`${
                                    localCustomer.email_verified
                                      ? "bg-green-100 text-green-800"
                                      : "bg-yellow-100 text-yellow-800"
                                  } text-xs px-1 py-0`}
                                >
                                  {localCustomer.email_verified
                                    ? "Verified"
                                    : "Pending"}
                                </Badge>
                              </div>
                              <div className="flex justify-between items-center">
                                <span className="text-xs text-gray-700">
                                  Member Since
                                </span>
                                <span className="text-xs text-gray-600">
                                  {formatDate(localCustomer.created_at)}
                                </span>
                              </div>
                            </div>
                          </CardContent>
                        </Card>

                        {/* Security & Privacy */}
                        <Card className="shadow-sm">
                          <CardContent className="p-1.5 sm:p-2 md:p-3">
                            <h4 className="font-semibold text-gray-900 mb-1 sm:mb-2 text-xs sm:text-sm ultra-small-title">
                              Security & Privacy
                            </h4>
                            <div className="space-y-1">
                              <Button
                                variant="outline"
                                className="w-full justify-start text-xs h-7"
                                size="sm"
                                onClick={() =>
                                  toast.info(
                                    "Two-Factor Authentication coming soon!"
                                  )
                                }
                              >
                                <Settings className="h-3 w-3 mr-1 ultra-small-icon" />
                                2FA
                              </Button>
                              <Button
                                variant="outline"
                                className="w-full justify-start text-xs h-7"
                                size="sm"
                                onClick={() =>
                                  toast.info("Login History coming soon!")
                                }
                              >
                                <Settings className="h-3 w-3 mr-1 ultra-small-icon" />
                                Login History
                              </Button>
                              <Button
                                variant="destructive"
                                className="w-full justify-start text-xs h-7"
                                size="sm"
                                onClick={() => setShowDeleteAccountModal(true)}
                              >
                                <Trash2 className="h-3 w-3 mr-1 ultra-small-icon" />
                                Delete Account
                              </Button>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Desktop Sidebar Layout */}
              <div className="hidden lg:flex flex-1 overflow-hidden">
                {/* Sidebar */}
                <div className="w-64 bg-white border-r border-slate-200 flex flex-col">
                  <div className="p-4 border-b border-slate-200">
                    <Button
                      onClick={() => setCurrentView("dashboard")}
                      className="w-full bg-transparent hover:bg-blue-600 hover:text-white text-blue-600 border border-blue-600 flex items-center justify-center gap-2 py-2 rounded-md transition-all duration-200"
                    >
                      <Home className="h-4 w-4" />
                      Dashboard
                    </Button>
                  </div>
                  <nav className="flex-1 p-4">
                    <ul className="space-y-2">
                      <li>
                        <Button
                          onClick={() => navigateToTab("bookings")}
                          className={`w-full justify-start ${
                            currentView === "bookings"
                              ? "bg-blue-600 text-white"
                              : "bg-transparent text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          <Calendar className="h-4 w-4 mr-3" />
                          My Bookings
                        </Button>
                      </li>
                      <li>
                        <Button
                          onClick={() => navigateToTab("payments")}
                          className={`w-full justify-start ${
                            currentView === "payments"
                              ? "bg-blue-600 text-white"
                              : "bg-transparent text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          <CreditCard className="h-4 w-4 mr-3" />
                          Payment History
                        </Button>
                      </li>
                      <li>
                        <Button
                          onClick={() => navigateToTab("settings")}
                          className={`w-full justify-start ${
                            currentView === "settings"
                              ? "bg-blue-600 text-white"
                              : "bg-transparent text-gray-700 hover:bg-gray-100"
                          }`}
                        >
                          <Settings className="h-4 w-4 mr-3" />
                          Settings
                        </Button>
                      </li>
                    </ul>
                  </nav>
                </div>

                {/* Main Content */}
                <div className="flex-1 flex flex-col overflow-hidden">
                  <div className="p-6 border-b border-slate-200">
                    <h2 className="text-2xl font-bold text-gray-900">
                      {currentView === "bookings" && "My Bookings"}
                      {currentView === "payments" && "Payment History"}
                      {currentView === "settings" && "Settings"}
                    </h2>
                  </div>

                  <div className="flex-1 overflow-y-auto p-6">
                    {currentView === "bookings" && (
                      <div className="space-y-6">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-gray-600">
                              {bookings.length} total bookings
                            </p>
                          </div>
                          <Button
                            onClick={(e) => handleCreateBooking(e)}
                            className="bg-blue-600 hover:bg-blue-700 text-white"
                          >
                            <Plus className="h-4 w-4 mr-2" />
                            Add Booking
                          </Button>
                        </div>

                        {loadingBookings ? (
                          <div className="text-center py-12">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p className="text-gray-600">Loading bookings...</p>
                          </div>
                        ) : bookings.length === 0 ? (
                          <div className="text-center py-12">
                            <Calendar className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                              No bookings yet
                            </h3>
                            <p className="text-gray-600 mb-4">
                              Start planning your next adventure
                            </p>
                            <Button
                              onClick={(e) => handleCreateBooking(e)}
                              className="bg-blue-600 hover:bg-blue-700 text-white"
                            >
                              <Plus className="h-4 w-4 mr-2" />
                              Create Your First Booking
                            </Button>
                          </div>
                        ) : (
                          <div className="grid gap-6">
                            {bookings.map((booking) => (
                              <Card
                                key={booking.id}
                                className="shadow-sm hover:shadow-md transition-shadow"
                              >
                                <CardContent className="p-6">
                                  <div className="flex justify-between items-start mb-4">
                                    <div>
                                      <h4 className="text-lg font-semibold text-gray-900 mb-1">
                                        {booking.destination}
                                      </h4>
                                      <p className="text-sm text-gray-600">
                                        {formatDate(booking.start_date)} -{" "}
                                        {formatDate(booking.end_date)}
                                      </p>
                                    </div>
                                    <Badge
                                      className={getStatusColor(booking.status)}
                                    >
                                      {booking.status}
                                    </Badge>
                                  </div>

                                  <div className="flex justify-between items-center mb-4">
                                    <div className="flex items-center gap-4">
                                      <div className="flex items-center gap-2">
                                        <User className="h-4 w-4" />
                                        <span className="text-sm">
                                          {booking.travelers} travelers
                                        </span>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <DollarSign className="h-4 w-4" />
                                        {formatCurrency(booking.price)}
                                      </div>
                                    </div>
                                  </div>

                                  <div className="flex justify-between items-center">
                                    <Badge
                                      className={getPaymentStatusColor(
                                        booking.payment_status
                                      )}
                                    >
                                      {booking.payment_status}
                                    </Badge>
                                    <div className="flex gap-2">
                                      <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() =>
                                          handleViewBooking(booking)
                                        }
                                      >
                                        <Eye className="h-4 w-4 mr-2" />
                                        View Details
                                      </Button>
                                      {booking.payment_status !== "paid" && (
                                        <Button
                                          variant="outline"
                                          size="sm"
                                          onClick={() => handlePayNow(booking)}
                                        >
                                          <CreditCard className="h-4 w-4 mr-2" />
                                          Pay Now
                                        </Button>
                                      )}
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {currentView === "payments" && (
                      <div className="space-y-6">
                        <div className="flex justify-between items-center">
                          <div>
                            <p className="text-gray-600">
                              Payment history and transactions
                            </p>
                          </div>
                          <div className="text-lg font-semibold text-gray-900">
                            Total:{" "}
                            {formatCurrency(
                              payments.reduce((sum, p) => sum + p.amount, 0)
                            )}
                          </div>
                        </div>

                        {loadingPayments ? (
                          <div className="text-center py-12">
                            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                            <p className="text-gray-600">Loading payments...</p>
                          </div>
                        ) : payments.length === 0 ? (
                          <div className="text-center py-12">
                            <CreditCard className="h-16 w-16 text-gray-400 mx-auto mb-4" />
                            <h3 className="text-lg font-semibold text-gray-900 mb-2">
                              No payments yet
                            </h3>
                            <p className="text-gray-600">
                              Your payment history will appear here
                            </p>
                          </div>
                        ) : (
                          <div className="grid gap-6">
                            {payments.map((payment) => (
                              <Card
                                key={payment.id}
                                className="shadow-sm hover:shadow-md transition-shadow"
                              >
                                <CardContent className="p-6">
                                  <div className="flex justify-between items-start mb-4">
                                    <div>
                                      <h4 className="text-lg font-semibold text-gray-900 mb-1">
                                        {payment.destination}
                                      </h4>
                                      <p className="text-sm text-gray-600">
                                        {formatDate(payment.created_at)}
                                      </p>
                                    </div>
                                    <Badge
                                      className={getPaymentStatusColor(
                                        payment.status
                                      )}
                                    >
                                      {payment.status}
                                    </Badge>
                                  </div>

                                  <div className="flex justify-between items-center">
                                    <div className="flex items-center gap-4">
                                      <div className="flex items-center gap-2">
                                        <DollarSign className="h-4 w-4" />
                                        <span className="font-semibold">
                                          {formatCurrency(payment.amount)}
                                        </span>
                                      </div>
                                      <div className="flex items-center gap-2">
                                        <CreditCard className="h-4 w-4" />
                                        {payment.payment_method}
                                      </div>
                                    </div>
                                  </div>
                                </CardContent>
                              </Card>
                            ))}
                          </div>
                        )}
                      </div>
                    )}

                    {currentView === "settings" && (
                      <div className="space-y-6">
                        <div className="grid grid-cols-1 xl:grid-cols-2 gap-6">
                          {/* Profile Settings */}
                          <Card className="shadow-sm hover:shadow-md transition-shadow">
                            <CardContent className="p-6">
                              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                Profile Information
                              </h3>
                              <div className="space-y-4">
                                <div>
                                  <label className="text-sm font-medium text-gray-700">
                                    Name
                                  </label>
                                  <p className="text-gray-600 mt-1">
                                    {localCustomer.name}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">
                                    Email
                                  </label>
                                  <p className="text-gray-600 mt-1">
                                    {localCustomer.email}
                                  </p>
                                </div>
                                <div>
                                  <label className="text-sm font-medium text-gray-700">
                                    Phone
                                  </label>
                                  <p className="text-gray-600 mt-1">
                                    {localCustomer.phone || "Not provided"}
                                  </p>
                                </div>
                                <Button
                                  variant="outline"
                                  className="w-full"
                                  onClick={() => setShowEditProfileModal(true)}
                                >
                                  <Edit className="h-4 w-4 mr-2" />
                                  Edit Profile
                                </Button>
                              </div>
                            </CardContent>
                          </Card>

                          {/* Account Settings */}
                          <Card className="shadow-sm hover:shadow-md transition-shadow">
                            <CardContent className="p-6">
                              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                Account Settings
                              </h3>
                              <div className="space-y-3">
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() =>
                                    setShowChangePasswordModal(true)
                                  }
                                >
                                  <Settings className="h-4 w-4 mr-2" />
                                  Change Password
                                </Button>
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() => setShowNotificationModal(true)}
                                >
                                  <Bell className="h-4 w-4 mr-2" />
                                  Notification Preferences
                                </Button>
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() =>
                                    setShowEmailPreferencesModal(true)
                                  }
                                >
                                  <Mail className="h-4 w-4 mr-2" />
                                  Email Preferences
                                </Button>
                              </div>
                            </CardContent>
                          </Card>

                          {/* Account Status */}
                          <Card className="shadow-sm hover:shadow-md transition-shadow">
                            <CardContent className="p-6">
                              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                Account Status
                              </h3>
                              <div className="space-y-4">
                                <div className="flex justify-between items-center">
                                  <span className="text-gray-700">
                                    Email Verified
                                  </span>
                                  <Badge
                                    className={
                                      localCustomer.email_verified
                                        ? "bg-green-100 text-green-800"
                                        : "bg-yellow-100 text-yellow-800"
                                    }
                                  >
                                    {localCustomer.email_verified
                                      ? "Verified"
                                      : "Pending"}
                                  </Badge>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-gray-700">
                                    Member Since
                                  </span>
                                  <span className="text-gray-600">
                                    {formatDate(localCustomer.created_at)}
                                  </span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-gray-700">
                                    Total Bookings
                                  </span>
                                  <span className="text-gray-600 font-semibold">
                                    {bookings.length}
                                  </span>
                                </div>
                                <div className="flex justify-between items-center">
                                  <span className="text-gray-700">
                                    Total Spent
                                  </span>
                                  <span className="text-gray-600 font-semibold">
                                    {formatCurrency(
                                      payments.reduce(
                                        (sum, p) => sum + p.amount,
                                        0
                                      )
                                    )}
                                  </span>
                                </div>
                              </div>
                            </CardContent>
                          </Card>

                          {/* Security Settings */}
                          <Card className="shadow-sm hover:shadow-md transition-shadow">
                            <CardContent className="p-6">
                              <h3 className="text-lg font-semibold text-gray-900 mb-4">
                                Security & Privacy
                              </h3>
                              <div className="space-y-3">
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() =>
                                    toast.info(
                                      "Two-Factor Authentication coming soon!"
                                    )
                                  }
                                >
                                  <Settings className="h-4 w-4 mr-2" />
                                  Two-Factor Authentication
                                </Button>
                                <Button
                                  variant="outline"
                                  className="w-full justify-start"
                                  onClick={() =>
                                    toast.info("Login History coming soon!")
                                  }
                                >
                                  <Settings className="h-4 w-4 mr-2" />
                                  Login History
                                </Button>
                                <Button
                                  variant="destructive"
                                  className="w-full justify-start"
                                  onClick={() =>
                                    setShowDeleteAccountModal(true)
                                  }
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Account
                                </Button>
                              </div>
                            </CardContent>
                          </Card>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Footer - Fixed at bottom */}
      <footer className="bg-gray-50 border-t border-gray-200 py-0.5 sm:py-1 px-1 sm:px-2 md:px-3">
        <div className="flex flex-col sm:flex-row justify-between items-center text-xs text-gray-600">
          <div className="hide-on-ultra-small sm:mb-0">
            Logged in as{" "}
            <span className="font-medium text-gray-900">
              {localCustomer.name}
            </span>
          </div>
          <div className="show-on-ultra-small text-center">
            <span className="font-medium text-gray-900 text-xs">
              {localCustomer.name}
            </span>
          </div>
          <div className="hide-on-ultra-small">
            {new Date().toLocaleDateString("en-US", {
              year: "numeric",
              month: "long",
              day: "numeric",
            })}{" "}
            •{" "}
            {new Date().toLocaleTimeString("en-US", {
              hour: "2-digit",
              minute: "2-digit",
            })}
          </div>
        </div>
      </footer>

      {/* Edit Profile Modal */}
      <Dialog
        open={showEditProfileModal}
        onOpenChange={setShowEditProfileModal}
      >
        <DialogContent className="w-[95vw] max-w-[350px] sm:max-w-[425px] mx-2 rounded-xl ultra-small-modal-content">
          <DialogHeader>
            <DialogTitle>Edit Profile</DialogTitle>
            <DialogDescription>
              Make changes to your profile here. Click save when you're done.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleEditProfile}>
            <div className="grid gap-4 py-4">
              <div className="flex flex-col space-y-2 sm:grid sm:grid-cols-4 sm:items-center sm:gap-4">
                <Label
                  htmlFor="name"
                  className="text-left sm:text-right font-medium ultra-small-modal-label"
                >
                  Name
                </Label>
                <Input
                  id="name"
                  value={editProfileData.name}
                  onChange={(e) =>
                    setEditProfileData({
                      ...editProfileData,
                      name: e.target.value,
                    })
                  }
                  className="sm:col-span-3 modal-input ultra-small-modal-input"
                  required
                />
              </div>
              <div className="flex flex-col space-y-2 sm:grid sm:grid-cols-4 sm:items-center sm:gap-4">
                <Label
                  htmlFor="email"
                  className="text-left sm:text-right font-medium ultra-small-modal-label"
                >
                  Email
                </Label>
                <Input
                  id="email"
                  type="email"
                  value={editProfileData.email}
                  onChange={(e) =>
                    setEditProfileData({
                      ...editProfileData,
                      email: e.target.value,
                    })
                  }
                  className="sm:col-span-3 modal-input ultra-small-modal-input"
                  required
                />
              </div>
              <div className="flex flex-col space-y-2 sm:grid sm:grid-cols-4 sm:items-center sm:gap-4">
                <Label
                  htmlFor="phone"
                  className="text-left sm:text-right font-medium ultra-small-modal-label"
                >
                  Phone
                </Label>
                <Input
                  id="phone"
                  value={editProfileData.phone}
                  onChange={(e) =>
                    setEditProfileData({
                      ...editProfileData,
                      phone: e.target.value,
                    })
                  }
                  className="sm:col-span-3 modal-input ultra-small-modal-input"
                />
              </div>
            </div>
            <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowEditProfileModal(false)}
                className="w-full sm:w-auto order-2 sm:order-1 ultra-small-modal-button"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full sm:w-auto order-1 sm:order-2 bg-green-600 hover:bg-green-700 text-white ultra-small-modal-button"
              >
                {isSubmitting ? "Saving..." : "Save Changes"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Change Password Modal */}
      <Dialog
        open={showChangePasswordModal}
        onOpenChange={setShowChangePasswordModal}
      >
        <DialogContent className="w-[95vw] max-w-[350px] sm:max-w-[425px] mx-2 rounded-xl ultra-small-modal-content">
          <DialogHeader>
            <DialogTitle>Change Password</DialogTitle>
            <DialogDescription>
              Enter your current password and choose a new one.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleChangePassword}>
            <div className="grid gap-4 py-4">
              <div className="flex flex-col space-y-2 sm:grid sm:grid-cols-4 sm:items-center sm:gap-4">
                <Label
                  htmlFor="currentPassword"
                  className="text-left sm:text-right ultra-small-modal-label"
                >
                  Current
                </Label>
                <Input
                  id="currentPassword"
                  type="password"
                  value={passwordData.currentPassword}
                  onChange={(e) =>
                    setPasswordData({
                      ...passwordData,
                      currentPassword: e.target.value,
                    })
                  }
                  className="sm:col-span-3 modal-input ultra-small-modal-input"
                  required
                />
              </div>
              <div className="flex flex-col space-y-2 sm:grid sm:grid-cols-4 sm:items-center sm:gap-4">
                <Label
                  htmlFor="newPassword"
                  className="text-left sm:text-right ultra-small-modal-label"
                >
                  New
                </Label>
                <Input
                  id="newPassword"
                  type="password"
                  value={passwordData.newPassword}
                  onChange={(e) =>
                    setPasswordData({
                      ...passwordData,
                      newPassword: e.target.value,
                    })
                  }
                  className="sm:col-span-3 modal-input ultra-small-modal-input"
                  required
                  minLength={6}
                />
              </div>
              <div className="flex flex-col space-y-2 sm:grid sm:grid-cols-4 sm:items-center sm:gap-4">
                <Label
                  htmlFor="confirmPassword"
                  className="text-left sm:text-right ultra-small-modal-label"
                >
                  Confirm
                </Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  value={passwordData.confirmPassword}
                  onChange={(e) =>
                    setPasswordData({
                      ...passwordData,
                      confirmPassword: e.target.value,
                    })
                  }
                  className="sm:col-span-3 modal-input ultra-small-modal-input"
                  required
                  minLength={6}
                />
              </div>
            </div>
            <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2">
              <Button
                type="button"
                variant="outline"
                onClick={() => {
                  setShowChangePasswordModal(false);
                  setPasswordData({
                    currentPassword: "",
                    newPassword: "",
                    confirmPassword: "",
                  });
                }}
                className="w-full sm:w-auto order-2 sm:order-1 ultra-small-modal-button"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full sm:w-auto order-1 sm:order-2 bg-green-600 hover:bg-green-700 text-white ultra-small-modal-button"
              >
                {isSubmitting ? "Changing..." : "Change Password"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Notification Preferences Modal */}
      <Dialog
        open={showNotificationModal}
        onOpenChange={setShowNotificationModal}
      >
        <DialogContent className="w-[95vw] max-w-[350px] sm:max-w-[425px] mx-2 rounded-xl ultra-small-modal-content">
          <DialogHeader>
            <DialogTitle>Notification Preferences</DialogTitle>
            <DialogDescription>
              Choose how you want to be notified about your bookings and account
              updates.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium ultra-small-modal-label">
                  Email Notifications
                </h4>
                <p className="text-sm text-gray-600 ultra-small-modal-description">
                  Receive booking updates via email
                </p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 rounded"
                defaultChecked
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium ultra-small-modal-label">
                  SMS Notifications
                </h4>
                <p className="text-sm text-gray-600 ultra-small-modal-description">
                  Get text messages for urgent updates
                </p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium ultra-small-modal-label">
                  Marketing Emails
                </h4>
                <p className="text-sm text-gray-600 ultra-small-modal-description">
                  Receive promotional offers and travel deals
                </p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 rounded"
                defaultChecked
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={() => setShowNotificationModal(false)}
              className="bg-green-600 hover:bg-green-700 text-white ultra-small-modal-button"
            >
              Save Preferences
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Email Preferences Modal */}
      <Dialog
        open={showEmailPreferencesModal}
        onOpenChange={setShowEmailPreferencesModal}
      >
        <DialogContent className="w-[95vw] max-w-[350px] sm:max-w-[425px] mx-2 rounded-xl ultra-small-modal-content">
          <DialogHeader>
            <DialogTitle>Email Preferences</DialogTitle>
            <DialogDescription>
              Choose which emails you want to receive.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4 space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium ultra-small-modal-label">
                  Booking Confirmations
                </h4>
                <p className="text-sm text-gray-600 ultra-small-modal-description">
                  Receive confirmations for new bookings
                </p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 rounded"
                defaultChecked
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium ultra-small-modal-label">
                  Payment Receipts
                </h4>
                <p className="text-sm text-gray-600 ultra-small-modal-description">
                  Get receipts for all payments
                </p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 rounded"
                defaultChecked
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium ultra-small-modal-label">
                  Travel Reminders
                </h4>
                <p className="text-sm text-gray-600 ultra-small-modal-description">
                  Reminders before your trip dates
                </p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 rounded"
                defaultChecked
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium ultra-small-modal-label">
                  Newsletter
                </h4>
                <p className="text-sm text-gray-600 ultra-small-modal-description">
                  Monthly travel tips and destination guides
                </p>
              </div>
              <input
                type="checkbox"
                className="h-4 w-4 text-blue-600 rounded"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              onClick={() => setShowEmailPreferencesModal(false)}
              variant="destructive"
              className="ultra-small-modal-button"
            >
              Close
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Account Modal */}
      <Dialog
        open={showDeleteAccountModal}
        onOpenChange={setShowDeleteAccountModal}
      >
        <DialogContent className="w-[95vw] max-w-[350px] sm:max-w-[425px] mx-2 rounded-xl ultra-small-modal-content">
          <DialogHeader>
            <DialogTitle>Delete Account</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete your account? This action cannot
              be undone. All your bookings and data will be permanently removed.
            </DialogDescription>
          </DialogHeader>
          <div className="py-4">
            <div className="bg-red-50 border border-red-200 rounded-md p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <XCircle className="h-5 w-5 text-red-400" />
                </div>
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800 ultra-small-modal-label">
                    Warning
                  </h3>
                  <div className="mt-2 text-sm text-red-700 ultra-small-modal-description">
                    <p>
                      This will permanently delete your account and all
                      associated data. You will lose access to all your bookings
                      and payment history.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <DialogFooter className="flex flex-col sm:flex-row gap-3 sm:gap-2">
            <Button
              type="button"
              variant="outline"
              onClick={() => setShowDeleteAccountModal(false)}
              className="w-full sm:w-auto order-2 sm:order-1 ultra-small-modal-button"
            >
              Cancel
            </Button>
            <Button
              type="button"
              variant="destructive"
              onClick={handleDeleteAccount}
              disabled={isSubmitting}
              className="w-full sm:w-auto order-1 sm:order-2 ultra-small-modal-button"
            >
              {isSubmitting ? "Deleting..." : "Delete Account"}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* New Booking Modal */}
      <Dialog open={showBookingModal} onOpenChange={setShowBookingModal}>
        <DialogContent className="w-[98vw] max-w-[320px] xs:max-w-[340px] sm:max-w-[380px] md:max-w-[425px] mx-1 sm:mx-2 rounded-lg sm:rounded-xl max-h-[95vh] overflow-y-auto modal-small-screen ultra-small-modal-content">
          <DialogHeader>
            <DialogTitle>Create New Booking</DialogTitle>
            <DialogDescription>
              Plan your next adventure. Fill in the details below.
            </DialogDescription>
          </DialogHeader>
          <form onSubmit={handleBookingSubmit}>
            <div className="grid gap-3 sm:gap-4 py-2 sm:py-4">
              <div className="grid gap-1 sm:gap-2">
                <Label
                  htmlFor="destination"
                  className="ultra-small-modal-label"
                >
                  Destination
                </Label>
                <Input
                  id="destination"
                  placeholder="Where would you like to go?"
                  value={bookingForm.destination}
                  onChange={(e) =>
                    setBookingForm({
                      ...bookingForm,
                      destination: e.target.value,
                    })
                  }
                  className="modal-input ultra-small-modal-input"
                  required
                />
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div className="grid gap-1 sm:gap-2">
                  <Label
                    htmlFor="start_date"
                    className="ultra-small-modal-label"
                  >
                    Start Date
                  </Label>
                  <Input
                    id="start_date"
                    type="date"
                    value={bookingForm.start_date}
                    onChange={(e) =>
                      setBookingForm({
                        ...bookingForm,
                        start_date: e.target.value,
                      })
                    }
                    className="modal-input ultra-small-modal-input"
                    required
                  />
                </div>
                <div className="grid gap-1 sm:gap-2">
                  <Label htmlFor="end_date" className="ultra-small-modal-label">
                    End Date
                  </Label>
                  <Input
                    id="end_date"
                    type="date"
                    value={bookingForm.end_date}
                    onChange={(e) =>
                      setBookingForm({
                        ...bookingForm,
                        end_date: e.target.value,
                      })
                    }
                    className="modal-input ultra-small-modal-input"
                    required
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                <div className="grid gap-1 sm:gap-2">
                  <Label
                    htmlFor="travelers"
                    className="ultra-small-modal-label"
                  >
                    Travelers
                  </Label>
                  <Input
                    id="travelers"
                    type="number"
                    min="1"
                    max="20"
                    value={bookingForm.travelers}
                    onChange={(e) =>
                      setBookingForm({
                        ...bookingForm,
                        travelers: parseInt(e.target.value),
                      })
                    }
                    className="modal-input ultra-small-modal-input"
                    required
                  />
                </div>
                <div className="grid gap-1 sm:gap-2">
                  <Label htmlFor="budget" className="ultra-small-modal-label">
                    Budget (USD)
                  </Label>
                  <Input
                    id="budget"
                    type="number"
                    min="0"
                    step="0.01"
                    placeholder="0.00"
                    value={bookingForm.budget}
                    onChange={(e) =>
                      setBookingForm({ ...bookingForm, budget: e.target.value })
                    }
                    className="modal-input ultra-small-modal-input"
                  />
                </div>
              </div>

              <div className="grid gap-1 sm:gap-2">
                <Label htmlFor="notes" className="ultra-small-modal-label">
                  Special Requests (Optional)
                </Label>
                <Input
                  id="notes"
                  placeholder="Any special requirements or notes..."
                  value={bookingForm.notes}
                  onChange={(e) =>
                    setBookingForm({ ...bookingForm, notes: e.target.value })
                  }
                  className="modal-input ultra-small-modal-input"
                />
              </div>
            </div>
            <DialogFooter className="flex flex-col sm:flex-row gap-2 sm:gap-3 pt-2 sm:pt-4">
              <Button
                type="button"
                variant="outline"
                onClick={() => setShowBookingModal(false)}
                className="w-full sm:w-auto order-2 sm:order-1 ultra-small-modal-button"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="w-full sm:w-auto order-1 sm:order-2 bg-green-600 hover:bg-green-700 text-white ultra-small-modal-button"
              >
                {isSubmitting ? "Creating..." : "Create Booking"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
    </div>
  );
}
