import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    // Check if this is an admin request (for admin dashboard, return all posts)
    const url = new URL(request.url);
    const isAdmin = url.searchParams.get("admin") === "true";

    console.log(`📥 FETCHING BLOG POSTS: isAdmin=${isAdmin}`);

    let sqlQuery;
    if (isAdmin) {
      // For admin: return all posts including unpublished, with content
      sqlQuery = `SELECT id, title, content, excerpt, author, slug, image_url as image, created_at as date, published
                  FROM blog_posts
                  ORDER BY created_at DESC`;
    } else {
      // For public: return only published posts without content
      sqlQuery = `SELECT id, title, excerpt, author, slug, image_url as image, created_at as date, published
                  FROM blog_posts
                  WHERE published = true
                  ORDER BY created_at DESC`;
    }

    const result = await query(sqlQuery);

    console.log(`📊 BLOG POSTS FETCHED: ${result.rows.length} posts`);

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("💥 BLOG API ERROR:", error);
    return NextResponse.json(
      { error: "Failed to fetch blog posts" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { title, content, excerpt, author, image, published = false } = body;

    console.log(`🆕 CREATING NEW BLOG POST`);
    console.log(`📝 POST DATA:`, {
      title,
      content,
      excerpt,
      author,
      image,
      published,
    });

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");

    console.log(`🔗 GENERATED SLUG:`, slug);

    const result = await query(
      `INSERT INTO blog_posts (title, content, excerpt, author, slug, image_url, published)
       VALUES ($1, $2, $3, $4, $5, $6, $7)
       RETURNING id, title, content, excerpt, author, slug, image_url as image, created_at as date, published`,
      [title, content, excerpt, author, slug, image, published]
    );

    console.log(`📊 INSERT RESULT:`, result);
    console.log(`📈 ROWS INSERTED:`, result.rowCount);
    console.log(`🆔 NEW POST ID:`, result.rows[0].id);

    const newPost = result.rows[0];
    console.log(`✅ BLOG POST CREATED SUCCESSFULLY: ID=${newPost.id}`);

    return NextResponse.json(newPost);
  } catch (error) {
    console.error("💥 BLOG CREATION ERROR:", error);
    return NextResponse.json(
      { error: "Failed to create blog post" },
      { status: 500 }
    );
  }
}
