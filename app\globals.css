@tailwind base;
@tailwind components;
@tailwind utilities;

/* Custom styles for all modal inputs */
@layer components {
  .modal-input {
    @apply focus:ring-2 focus:ring-blue-500 focus:border-blue-500 focus-visible:ring-2 focus-visible:ring-blue-500 focus-visible:border-blue-500;
  }

  .modal-input:focus {
    --tw-ring-color: rgb(59 130 246) !important;
    border-color: rgb(59 130 246) !important;
    box-shadow: 0 0 0 2px rgb(59 130 246 / 0.2) !important;
  }

  .modal-input:focus-visible {
    --tw-ring-color: rgb(59 130 246) !important;
    border-color: rgb(59 130 246) !important;
    box-shadow: 0 0 0 2px rgb(59 130 246 / 0.2) !important;
  }

  /* Legacy class for backward compatibility */
  .booking-input {
    @apply modal-input;
  }

  /* Small screen modal optimizations */
  .modal-small-screen {
    @apply text-sm;
  }

  @media (max-width: 375px) {
    .modal-small-screen {
      font-size: 0.8rem;
    }
  }

  @media (max-width: 320px) {
    .modal-small-screen {
      font-size: 0.75rem;
    }
  }

  /* Ultra small screen optimizations for iPhone 5/SE */
  .ultra-small-screen {
    @apply text-xs;
  }

  @media (max-width: 320px) {
    .ultra-small-screen {
      font-size: 0.75rem;
      line-height: 1.2;
    }

    /* Cards */
    .ultra-small-card {
      padding: 0.5rem !important;
      width: 110px !important;
      height: 110px !important;
      aspect-ratio: 1 !important;
      border-radius: 0.75rem !important;
      display: flex !important;
      align-items: center !important;
      justify-content: center !important;
    }

    /* Typography */
    .ultra-small-title {
      font-size: 1rem !important;
      line-height: 1.2 !important;
      margin-bottom: 0.5rem !important;
      font-weight: 700 !important;
    }

    .ultra-small-subtitle {
      font-size: 0.625rem !important;
      line-height: 1.1 !important;
      margin-bottom: 0.125rem !important;
    }

    .ultra-small-icon {
      width: 1.25rem !important;
      height: 1.25rem !important;
    }

    .ultra-small-number {
      font-size: 1rem !important;
      line-height: 1.2 !important;
      font-weight: 700 !important;
    }

    /* Visibility controls */
    .hide-on-ultra-small {
      display: none !important;
    }

    .show-on-ultra-small {
      display: block !important;
    }

    /* Layout adjustments */
    .ultra-small-grid {
      grid-template-columns: 110px 110px !important;
      gap: 1rem !important;
      padding: 1rem !important;
      max-width: 280px !important;
      margin: 0 auto !important;
      justify-content: center !important;
      justify-items: center !important;
    }

    .ultra-small-navbar {
      height: 2.5rem !important;
      padding: 0.25rem 0.5rem !important;
    }

    .ultra-small-main {
      padding: 0.5rem !important;
      padding-top: 1rem !important;
      padding-bottom: 1rem !important;
    }

    /* Settings page specific */
    .ultra-small-settings {
      padding: 0.5rem !important;
      font-size: 0.75rem !important;
    }

    .ultra-small-settings h2 {
      font-size: 1rem !important;
      margin-bottom: 0.5rem !important;
    }

    .ultra-small-settings h3 {
      font-size: 0.875rem !important;
      margin-bottom: 0.375rem !important;
    }

    .ultra-small-settings .card {
      padding: 0.5rem !important;
      margin-bottom: 0.5rem !important;
    }

    .ultra-small-settings input,
    .ultra-small-settings button {
      font-size: 0.75rem !important;
      padding: 0.375rem !important;
      height: auto !important;
    }

    .ultra-small-settings label {
      font-size: 0.75rem !important;
      margin-bottom: 0.25rem !important;
    }

    /* Tab navigation */
    .ultra-small-tabs {
      height: 2.5rem !important;
      padding: 0.125rem !important;
    }

    .ultra-small-tabs button {
      font-size: 0.625rem !important;
      padding: 0.25rem !important;
    }

    .ultra-small-tabs .icon {
      width: 0.75rem !important;
      height: 0.75rem !important;
    }

    /* Additional ultra-small optimizations */
    .ultra-small-container {
      padding: 0.25rem !important;
      margin: 0 !important;
    }

    .ultra-small-spacing {
      margin-bottom: 0.25rem !important;
      margin-top: 0.25rem !important;
    }

    /* Ensure proper text wrapping */
    .ultra-small-text {
      word-wrap: break-word !important;
      overflow-wrap: break-word !important;
      hyphens: auto !important;
    }

    /* Prevent horizontal overflow */
    .ultra-small-no-overflow {
      overflow-x: hidden !important;
      max-width: 100vw !important;
    }

    /* Dashboard layout for ultra small screens */
    .ultra-small-dashboard {
      min-height: calc(100vh - 120px) !important;
      display: flex !important;
      flex-direction: column !important;
      justify-content: center !important;
      align-items: center !important;
    }

    /* Logout button for ultra small screens */
    .ultra-small-logout {
      width: 1.5rem !important;
      height: 1.5rem !important;
      padding: 0.125rem !important;
      min-width: 1.5rem !important;
    }

    .ultra-small-logout svg {
      width: 0.75rem !important;
      height: 0.75rem !important;
    }

    /* Modal optimizations for ultra small screens */
    .ultra-small-modal {
      width: calc(100vw - 1rem) !important;
      max-width: calc(100vw - 1rem) !important;
      height: auto !important;
      max-height: calc(100vh - 2rem) !important;
      margin: 0.5rem !important;
      border-radius: 0.75rem !important;
    }

    .ultra-small-modal-header {
      padding: 0.75rem !important;
      border-bottom: 1px solid #e5e7eb !important;
    }

    .ultra-small-modal-title {
      font-size: 0.875rem !important;
      font-weight: 600 !important;
      line-height: 1.2 !important;
    }

    .ultra-small-modal-content {
      padding: 0.75rem !important;
      font-size: 0.75rem !important;
      overflow-y: auto !important;
      flex: 1 !important;
    }

    .ultra-small-modal-close {
      width: 1.5rem !important;
      height: 1.5rem !important;
      padding: 0.125rem !important;
    }

    .ultra-small-modal-close svg {
      width: 0.75rem !important;
      height: 0.75rem !important;
    }

    .ultra-small-modal-input {
      font-size: 0.75rem !important;
      padding: 0.375rem !important;
      height: 2rem !important;
    }

    .ultra-small-modal-button {
      font-size: 0.75rem !important;
      padding: 0.375rem 0.75rem !important;
      height: 2rem !important;
    }

    .ultra-small-modal-label {
      font-size: 0.75rem !important;
      font-weight: 500 !important;
      margin-bottom: 0.25rem !important;
    }

    /* FORCE MODAL CENTERING - FINAL SOLUTION */
    @media (max-width: 768px) {
      /* Override ALL Radix Dialog positioning */
      [data-radix-dialog-content] {
        position: fixed !important;
        left: 50% !important;
        top: 50% !important;
        right: auto !important;
        bottom: auto !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 90vw !important;
        max-width: 320px !important;
        max-height: 90vh !important;
        overflow-y: auto !important;
      }

      /* Remove any conflicting transforms or positioning */
      [data-radix-dialog-content].ultra-small-modal,
      [data-radix-dialog-content].ultra-small-modal-content,
      [data-radix-dialog-content].modal-small-screen {
        position: fixed !important;
        left: 50% !important;
        top: 50% !important;
        right: auto !important;
        bottom: auto !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 90vw !important;
        max-width: 320px !important;
      }

      /* Ensure overlay doesn't interfere */
      [data-radix-dialog-overlay] {
        position: fixed !important;
        inset: 0 !important;
        z-index: 50 !important;
      }

      /* Nuclear option - override any inline styles */
      [data-radix-dialog-content][style] {
        position: fixed !important;
        left: 50% !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 90vw !important;
        max-width: 320px !important;
      }
    }

    /* Additional mobile-specific overrides */
    @media (max-width: 480px) {
      [data-radix-dialog-content] {
        width: 85vw !important;
        max-width: 300px !important;
        padding: 0.875rem !important;
      }
    }

    /* Extra small screens - very narrow phones - FORCE PERFECT CENTERING */
    @media (max-width: 320px) {
      /* Nuclear option - override everything for tiny screens */
      [data-radix-dialog-content] {
        position: fixed !important;
        left: 50% !important;
        top: 50% !important;
        right: auto !important;
        bottom: auto !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 85vw !important;
        max-width: 280px !important;
        min-width: 250px !important;
        padding: 0.75rem !important;
        z-index: 9999 !important;
      }

      /* Override any inline styles completely */
      [data-radix-dialog-content][style] {
        position: fixed !important;
        left: 50% !important;
        top: 50% !important;
        right: auto !important;
        bottom: auto !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 85vw !important;
        max-width: 280px !important;
        min-width: 250px !important;
      }

      /* Force center any modal classes */
      .ultra-small-modal-content,
      .modal-small-screen,
      .ultra-small-modal {
        position: fixed !important;
        left: 50% !important;
        top: 50% !important;
        transform: translate(-50%, -50%) !important;
        margin: 0 !important;
        width: 85vw !important;
        max-width: 280px !important;
      }

      /* Smaller text and spacing for very small screens */
      .ultra-small-modal-content {
        font-size: 0.8rem !important;
      }

      .ultra-small-modal-title {
        font-size: 1rem !important;
      }

      .ultra-small-modal-label {
        font-size: 0.7rem !important;
      }

      .ultra-small-modal-input {
        padding: 0.5rem !important;
        font-size: 0.8rem !important;
      }

      .ultra-small-modal-button {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.8rem !important;
      }
    }
  }

  /* JavaScript fallback for modal centering */
  .force-center-modal {
    position: fixed !important;
    left: 50% !important;
    top: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
  }

  /* Ultra aggressive centering for very small screens */
  @media (max-width: 320px) {
    body [data-radix-dialog-content] {
      position: fixed !important;
      left: 50% !important;
      top: 50% !important;
      transform: translate(-50%, -50%) !important;
      margin: 0 !important;
      width: 70vw !important;
      max-width: 220px !important;
    }

    /* Override viewport units that might be causing issues */
    html [data-radix-dialog-content] {
      position: fixed !important;
      left: calc(50vw - 110px) !important;
      top: calc(50vh - 150px) !important;
      transform: none !important;
      width: 220px !important;
      max-width: 220px !important;
    }
  }

  .ultra-small-modal-description {
    font-size: 0.625rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.75rem !important;
  }

  /* Force center positioning for Dialog components */
  [data-radix-popper-content-wrapper] .ultra-small-modal,
  [data-state="open"] .ultra-small-modal {
    left: 50% !important;
    top: 50% !important;
    right: auto !important;
    bottom: auto !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
  }
}

/* Default behavior for larger screens */
@media (min-width: 321px) {
  .hide-on-ultra-small {
    display: block !important;
  }

  .show-on-ultra-small {
    display: none !important;
  }
}

:root {
  --foreground-rgb: 0, 0, 0;
  --background-start-rgb: 214, 219, 220;
  --background-end-rgb: 255, 255, 255;
}

@media (prefers-color-scheme: dark) {
  :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 15%;
    --card: 0 0% 100%;
    --card-foreground: 0 0% 15%;
    --popover: 0 0% 100%;
    --popover-foreground: 0 0% 15%;
    --primary: 11 94% 52%;
    --primary-foreground: 0 0% 100%;
    --secondary: 210 40% 98%;
    --secondary-foreground: 0 0% 15%;
    --muted: 210 40% 98%;
    --muted-foreground: 0 0% 45%;
    --accent: 210 40% 98%;
    --accent-foreground: 0 0% 15%;
    --destructive: 0 84% 60%;
    --destructive-foreground: 0 0% 98%;
    --border: 214 32% 91%;
    --input: 214 32% 91%;
    --ring: 11 94% 52%;
    --chart-1: 221 83% 53%;
    --chart-2: 221 83% 53%;
    --chart-3: 221 83% 53%;
    --chart-4: 221 83% 53%;
    --chart-5: 221 83% 53%;
    --radius: 0.5rem;
  }
  .dark {
    --background: 0 0% 3.9%;
    --foreground: 0 0% 98%;
    --card: 0 0% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 0 0% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 0 0% 98%;
    --primary-foreground: 0 0% 9%;
    --secondary: 0 0% 14.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 0 0% 14.9%;
    --muted-foreground: 0 0% 63.9%;
    --accent: 0 0% 14.9%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 0 0% 14.9%;
    --input: 0 0% 14.9%;
    --ring: 0 0% 83.1%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Mobile menu z-index fix */
.mobile-menu-overlay {
  z-index: 999999 !important;
}

.mobile-menu-panel {
  z-index: 1000000 !important;
}

/* Dropdown menu inside mobile menu */
.mobile-menu-panel [data-radix-popper-content-wrapper] {
  z-index: 1000001 !important;
}

.mobile-menu-panel [role="menu"] {
  z-index: 1000001 !important;
}

/* Prevent body scroll when mobile menu is open */
.no-scroll {
  overflow: hidden !important;
  touch-action: none !important;
  -webkit-overflow-scrolling: touch !important;
}

/* Custom responsive breakpoints for different devices */
@layer utilities {
  /* Android Smartphones */
  @media (min-width: 360px) and (max-width: 414px) {
    .android-sm {
      @apply text-xs;
    }
    .modal-android-sm {
      @apply h-[calc(100vh-8px)] w-[calc(100vw-8px)] rounded-lg;
    }
    .card-android-sm {
      @apply h-[calc(100vh-200px)] min-h-[350px];
    }
  }

  /* Large Android Smartphones */
  @media (min-width: 415px) and (max-width: 480px) {
    .android-lg {
      @apply text-sm;
    }
    .modal-android-lg {
      @apply h-[calc(100vh-16px)] w-[calc(100vw-16px)] rounded-lg;
    }
    .card-android-lg {
      @apply h-[calc(100vh-220px)] min-h-[380px];
    }
  }

  /* iPhone SE and similar */
  @media (min-width: 320px) and (max-width: 375px) {
    .iphone-se {
      @apply text-xs;
    }
    .modal-iphone-se {
      @apply h-[calc(100vh-8px)] w-[calc(100vw-8px)] rounded-md;
    }
    .card-iphone-se {
      @apply h-[calc(100vh-180px)] min-h-[320px];
    }
  }

  /* iPhone Standard (6/7/8/X/11/12/13) */
  @media (min-width: 375px) and (max-width: 428px) {
    .iphone-std {
      @apply text-sm;
    }
    .modal-iphone-std {
      @apply h-[calc(100vh-12px)] w-[calc(100vw-12px)] rounded-lg;
    }
    .card-iphone-std {
      @apply h-[calc(100vh-200px)] min-h-[350px];
    }
  }

  /* iPhone Plus/Pro Max */
  @media (min-width: 414px) and (max-width: 480px) {
    .iphone-plus {
      @apply text-sm;
    }
    .modal-iphone-plus {
      @apply h-[calc(100vh-16px)] w-[calc(100vw-16px)] rounded-lg;
    }
    .card-iphone-plus {
      @apply h-[calc(100vh-220px)] min-h-[380px];
    }
  }

  /* Tablets Portrait */
  @media (min-width: 768px) and (max-width: 1024px) and (orientation: portrait) {
    .tablet-portrait {
      @apply text-base;
    }
    .modal-tablet-portrait {
      @apply h-[calc(100vh-40px)] w-[calc(100vw-40px)] max-w-2xl rounded-xl;
    }
    .card-tablet-portrait {
      @apply h-[calc(100vh-160px)] min-h-[500px];
    }
  }

  /* Tablets Landscape */
  @media (min-width: 1024px) and (max-width: 1366px) and (orientation: landscape) {
    .tablet-landscape {
      @apply text-base;
    }
    .modal-tablet-landscape {
      @apply h-[calc(100vh-60px)] w-[calc(100vw-60px)] max-w-4xl rounded-xl;
    }
    .card-tablet-landscape {
      @apply h-[calc(100vh-180px)] min-h-[600px];
    }
  }

  /* Desktop Small (1366px - 1440px) */
  @media (min-width: 1366px) and (max-width: 1440px) {
    .desktop-sm {
      @apply text-base;
    }
    .modal-desktop-sm {
      @apply h-auto max-h-[calc(100vh-80px)] w-auto max-w-4xl rounded-xl;
    }
    .card-desktop-sm {
      @apply h-[calc(100vh-200px)] min-h-[650px];
    }
  }

  /* Desktop Medium (1441px - 1920px) */
  @media (min-width: 1441px) and (max-width: 1920px) {
    .desktop-md {
      @apply text-lg;
    }
    .modal-desktop-md {
      @apply h-auto max-h-[calc(100vh-100px)] w-auto max-w-5xl rounded-xl;
    }
    .card-desktop-md {
      @apply h-[calc(100vh-220px)] min-h-[700px];
    }
  }

  /* Desktop Large (1921px+) */
  @media (min-width: 1921px) {
    .desktop-lg {
      @apply text-xl;
    }
    .modal-desktop-lg {
      @apply h-auto max-h-[calc(100vh-120px)] w-auto max-w-6xl rounded-2xl;
    }
    .card-desktop-lg {
      @apply h-[calc(100vh-240px)] min-h-[800px];
    }
  }

  /* Ultra-wide monitors (21:9 aspect ratio) */
  @media (min-width: 2560px) {
    .ultrawide {
      @apply text-xl;
    }
    .modal-ultrawide {
      @apply h-auto max-h-[calc(100vh-140px)] w-auto max-w-7xl rounded-2xl;
    }
    .card-ultrawide {
      @apply h-[calc(100vh-260px)] min-h-[900px];
    }
  }

  /* Responsive modal classes */
  .modal-responsive {
    @apply modal-iphone-se;
    @apply sm:modal-iphone-std;
    @apply md:modal-tablet-portrait;
    @apply lg:modal-tablet-landscape;
    @apply xl:modal-desktop-sm;
    @apply 2xl:modal-desktop-md;
  }

  /* Responsive card classes */
  .card-responsive {
    @apply card-iphone-se;
    @apply sm:card-iphone-std;
    @apply md:card-tablet-portrait;
    @apply lg:card-tablet-landscape;
    @apply xl:card-desktop-sm;
    @apply 2xl:card-desktop-md;
  }

  /* Responsive text classes */
  .text-responsive {
    @apply iphone-se;
    @apply sm:iphone-std;
    @apply md:tablet-portrait;
    @apply lg:tablet-landscape;
    @apply xl:desktop-sm;
    @apply 2xl:desktop-md;
  }
}

/* Smooth scrolling for all elements */
* {
  scroll-behavior: smooth;
}

/* Custom scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}

/* Firefox scrollbar */
* {
  scrollbar-width: thin;
  scrollbar-color: #cbd5e1 #f1f5f9;
}

/* Prevent horizontal scroll on mobile */
@media (max-width: 768px) {
  body {
    overflow-x: hidden;
  }
}

/* Ensure modals are properly centered on all devices */
.modal-container {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 0.25rem;
}

/* Force modals to always be centered and visible */
@media (max-width: 640px) {
  .modal-container {
    align-items: center;
    justify-content: center;
    padding: 0.125rem;
  }
}

/* Prevent modal overflow on any device */
.modal-no-scroll {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
}

/* Modal content that fits viewport */
.modal-fit-viewport {
  max-height: calc(100vh - 8px);
  max-width: calc(100vw - 8px);
  height: calc(100vh - 8px);
  width: 100%;
  display: flex;
  flex-direction: column;
}

@media (min-width: 640px) {
  .modal-fit-viewport {
    max-height: calc(100vh - 16px);
    max-width: calc(100vw - 16px);
    height: calc(100vh - 16px);
  }
}

@media (min-width: 768px) {
  .modal-fit-viewport {
    max-height: calc(100vh - 32px);
    max-width: calc(100vw - 32px);
    height: calc(100vh - 32px);
  }
}

@media (min-width: 1024px) {
  .modal-fit-viewport {
    max-height: calc(100vh - 64px);
    max-width: calc(100vw - 64px);
    height: calc(100vh - 64px);
  }
}

@media (min-width: 1280px) {
  .modal-fit-viewport {
    max-height: calc(100vh - 80px);
    max-width: calc(100vw - 80px);
    height: calc(100vh - 80px);
  }
}

@media (min-width: 1536px) {
  .modal-fit-viewport {
    max-height: calc(100vh - 96px);
    max-width: calc(100vw - 96px);
    height: calc(100vh - 96px);
  }
}

/* Responsive table improvements */
@media (max-width: 768px) {
  .table-responsive {
    display: block;
    overflow-x: auto;
    white-space: nowrap;
  }

  .table-responsive table {
    min-width: 600px;
  }
}

/* Touch-friendly button sizing */
@media (max-width: 768px) {
  .btn-touch {
    min-height: 44px;
    min-width: 44px;
    padding: 0.75rem;
  }
}

/* Dashboard tabs no-scroll layout */
.dashboard-tab-content {
  height: calc(100vh - 120px);
  overflow: hidden;
}

@media (max-width: 320px) {
  .dashboard-tab-content {
    height: calc(100vh - 140px);
  }
}

@media (min-width: 321px) and (max-width: 640px) {
  .dashboard-tab-content {
    height: calc(100vh - 130px);
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .dashboard-tab-content {
    height: calc(100vh - 120px);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .dashboard-tab-content {
    height: calc(100vh - 110px);
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .dashboard-tab-content {
    height: calc(100vh - 100px);
  }
}

@media (min-width: 1281px) {
  .dashboard-tab-content {
    height: calc(100vh - 90px);
  }
}

/* Card perfect fit */
.card-perfect-fit {
  height: calc(100vh - 180px);
}

@media (max-width: 320px) {
  .card-perfect-fit {
    height: calc(100vh - 200px);
  }
}

@media (min-width: 321px) and (max-width: 375px) {
  .card-perfect-fit {
    height: calc(100vh - 190px);
  }
}

@media (min-width: 376px) and (max-width: 640px) {
  .card-perfect-fit {
    height: calc(100vh - 180px);
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .card-perfect-fit {
    height: calc(100vh - 170px);
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .card-perfect-fit {
    height: calc(100vh - 160px);
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .card-perfect-fit {
    height: calc(100vh - 150px);
  }
}

@media (min-width: 1281px) and (max-width: 1536px) {
  .card-perfect-fit {
    height: calc(100vh - 140px);
  }
}

@media (min-width: 1537px) {
  .card-perfect-fit {
    height: calc(100vh - 130px);
  }
}

/* Force no page scroll on dashboard tabs */
.dashboard-no-scroll {
  height: 100vh;
  overflow: hidden;
}

/* Tab content container - no scroll */
.tab-content-no-scroll {
  height: calc(100vh - 60px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Card container that fits perfectly */
.card-container-fit {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

/* Prevent footer overlap */
.dashboard-main-content {
  height: calc(100vh - 60px);
  overflow: hidden;
  position: relative;
}

/* Specific fixes for different screen sizes */
@media (max-width: 320px) {
  .dashboard-card-mobile-xs {
    height: calc(100vh - 150px) !important;
  }
}

@media (min-width: 321px) and (max-width: 375px) {
  .dashboard-card-mobile-sm {
    height: calc(100vh - 145px) !important;
  }
}

@media (min-width: 376px) and (max-width: 640px) {
  .dashboard-card-mobile-md {
    height: calc(100vh - 140px) !important;
  }
}

@media (min-width: 641px) and (max-width: 768px) {
  .dashboard-card-tablet-sm {
    height: calc(100vh - 135px) !important;
  }
}

@media (min-width: 769px) and (max-width: 1024px) {
  .dashboard-card-tablet-lg {
    height: calc(100vh - 130px) !important;
  }
}

@media (min-width: 1025px) and (max-width: 1280px) {
  .dashboard-card-desktop-sm {
    height: calc(100vh - 125px) !important;
  }
}

@media (min-width: 1281px) and (max-width: 1536px) {
  .dashboard-card-desktop-md {
    height: calc(100vh - 120px) !important;
  }
}

@media (min-width: 1537px) {
  .dashboard-card-desktop-lg {
    height: calc(100vh - 115px) !important;
  }
}

/* Ensure modals don't get cut off by footer */
.modal-safe-area {
  max-height: calc(100vh - 20px);
  margin-bottom: 10px;
}

/* Override any conflicting styles */
.dashboard-tab-override {
  height: 100vh !important;
  overflow: hidden !important;
}

.dashboard-tab-override .space-y-3,
.dashboard-tab-override .space-y-4,
.dashboard-tab-override .space-y-6 {
  height: calc(100vh - 80px) !important;
  overflow: hidden !important;
  display: flex !important;
  flex-direction: column !important;
}

.dashboard-tab-override .pb-2,
.dashboard-tab-override .pb-4,
.dashboard-tab-override .pb-12 {
  padding-bottom: 0 !important;
  flex: 1 !important;
  display: flex !important;
  flex-direction: column !important;
}
