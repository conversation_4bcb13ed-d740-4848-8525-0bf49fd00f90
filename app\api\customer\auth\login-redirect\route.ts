import { NextRequest, NextResponse } from "next/server";
import bcrypt from "bcryptjs";
import jwt from "jsonwebtoken";
import { query } from "@/lib/postgres";

export const dynamic = "force-dynamic";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

export async function POST(request: NextRequest) {
  try {
    console.log("🔐 LOGIN-REDIRECT API: Request received");

    let email: string;
    let password: string;

    const contentType = request.headers.get("content-type");

    if (contentType?.includes("application/json")) {
      const body = await request.json();
      email = body.email;
      password = body.password;
    } else {
      const formData = await request.formData();
      email = formData.get("email") as string;
      password = formData.get("password") as string;
    }

    if (!email || !password) {
      console.log("🔐 LOGIN-REDIRECT API: Missing email or password");
      return NextResponse.json(
        { message: "Email and password are required" },
        { status: 400 }
      );
    }

    console.log("🔐 LOGIN-REDIRECT API: Querying database");

    // Find customer by email
    const customerQuery = "SELECT * FROM customers WHERE email = $1";
    const customerResult = await query(customerQuery, [email]);

    if (customerResult.rows.length === 0) {
      console.log("🔐 LOGIN-REDIRECT API: Customer not found");
      return NextResponse.json(
        { message: "Invalid credentials" },
        { status: 401 }
      );
    }

    const customer = customerResult.rows[0];

    // Verify password
    console.log(`🔐 LOGIN-REDIRECT API: Comparing password for ${email}`);
    console.log(
      `🔐 LOGIN-REDIRECT API: Stored hash: ${customer.password_hash.substring(
        0,
        20
      )}...`
    );

    const isPasswordValid = await bcrypt.compare(
      password,
      customer.password_hash
    );
    console.log(`🔐 LOGIN-REDIRECT API: Password valid: ${isPasswordValid}`);

    if (!isPasswordValid) {
      console.log("🔐 LOGIN-REDIRECT API: Invalid password");
      return NextResponse.json(
        { message: "Invalid credentials" },
        { status: 401 }
      );
    }

    // Generate JWT token
    const token = jwt.sign(
      { customerId: customer.id, email: customer.email },
      JWT_SECRET,
      { expiresIn: "7d" }
    );

    console.log(
      "🔐 LOGIN-REDIRECT API: Login successful, creating redirect response"
    );

    // Remove password hash from response
    const { password_hash, ...customerData } = customer;

    // Create response with redirect
    const response = NextResponse.redirect(
      new URL("/customer/dashboard", request.url)
    );

    // Set cookies
    response.cookies.set("customer_token", token, {
      httpOnly: false,
      secure: process.env.NODE_ENV === "production",
      sameSite: "strict",
      maxAge: 7 * 24 * 60 * 60, // 7 days
      path: "/",
    });

    console.log("🔐 LOGIN-REDIRECT API: Redirect response created");
    return response;
  } catch (error) {
    console.error("🔐 LOGIN-REDIRECT API: Error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
