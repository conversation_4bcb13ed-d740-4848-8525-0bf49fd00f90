import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";

export const dynamic = "force-dynamic";

// PUT - Update a blog post
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const {
      title,
      content,
      excerpt,
      author,
      image,
      published = false,
    } = await request.json();
    const postId = parseInt(id);

    console.log(`🔄 UPDATING BLOG POST: ID=${postId}`);
    console.log(`📝 UPDATE DATA:`, {
      title,
      content,
      excerpt,
      author,
      image,
      published,
    });

    // Generate slug from title
    const slug = title
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, "-")
      .replace(/(^-|-$)/g, "");

    console.log(`🔗 GENERATED SLUG:`, slug);

    const result = await query(
      `UPDATE blog_posts
       SET title = $1, content = $2, excerpt = $3, author = $4, slug = $5, image_url = $6, published = $7, updated_at = CURRENT_TIMESTAMP
       WHERE id = $8
       RETURNING id, title, content, excerpt, author, slug, image_url as image, created_at as date, published`,
      [title, content, excerpt, author, slug, image, published, postId]
    );

    console.log(`📊 UPDATE RESULT:`, result);
    console.log(`📈 ROWS AFFECTED:`, result.rowCount);

    if (result.rowCount === 0) {
      console.log(`❌ BLOG POST NOT FOUND: ID=${postId}`);
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    const updatedPost = result.rows[0];
    console.log(`✅ BLOG POST UPDATED SUCCESSFULLY: ID=${postId}`);

    return NextResponse.json(updatedPost);
  } catch (error) {
    console.error("💥 BLOG UPDATE ERROR:", error);
    return NextResponse.json(
      { error: "Failed to update blog post" },
      { status: 500 }
    );
  }
}

// DELETE - Delete a blog post
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;
  try {
    const postId = parseInt(id);

    console.log(`🗑️ DELETING BLOG POST: ID=${postId}`);

    const result = await query("DELETE FROM blog_posts WHERE id = $1", [
      postId,
    ]);

    console.log(`📊 DELETE RESULT:`, result);
    console.log(`📈 ROWS AFFECTED:`, result.rowCount);

    if (result.rowCount === 0) {
      console.log(`❌ BLOG POST NOT FOUND: ID=${postId}`);
      return NextResponse.json(
        { error: "Blog post not found" },
        { status: 404 }
      );
    }

    console.log(`✅ BLOG POST DELETED SUCCESSFULLY: ID=${postId}`);
    return NextResponse.json({
      message: "Blog post deleted successfully",
    });
  } catch (error) {
    console.error("💥 BLOG DELETE ERROR:", error);
    return NextResponse.json(
      { error: "Failed to delete blog post" },
      { status: 500 }
    );
  }
}
