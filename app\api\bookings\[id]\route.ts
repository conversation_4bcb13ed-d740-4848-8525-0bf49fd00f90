import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";

export const dynamic = "force-dynamic";

// PATCH - Update booking status
export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  try {
    const { status } = await request.json();
    const bookingId = parseInt(id);

    // Validate status
    const validStatuses = ["pending", "confirmed", "cancelled", "completed"];
    if (!validStatuses.includes(status)) {
      return NextResponse.json(
        {
          error:
            "Invalid status. Must be one of: pending, confirmed, cancelled, completed",
        },
        { status: 400 }
      );
    }

    const result = await query(
      "UPDATE bookings SET status = $1 WHERE id = $2",
      [status, bookingId]
    );

    if (result.rowCount === 0) {
      return NextResponse.json({ error: "Booking not found" }, { status: 404 });
    }

    return NextResponse.json({
      message: "Booking status updated successfully",
      id: bookingId,
      status: status,
    });
  } catch (error) {
    console.error("Booking status update error:", error);
    return NextResponse.json(
      { error: "Failed to update booking status" },
      { status: 500 }
    );
  }
}

// PUT - Update a booking
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  try {
    const {
      name,
      email,
      phone,
      destination,
      startDate,
      endDate,
      travelers,
      requirements,
    } = await request.json();
    const bookingId = parseInt(id);

    const result = await query(
      `UPDATE bookings
       SET name = $1, email = $2, phone = $3, destination = $4, start_date = $5, end_date = $6, travelers = $7, requirements = $8
       WHERE id = $9`,
      [
        name,
        email,
        phone,
        destination,
        startDate,
        endDate,
        travelers,
        requirements,
        bookingId,
      ]
    );

    if (result.rowCount === 0) {
      return NextResponse.json({ error: "Booking not found" }, { status: 404 });
    }

    return NextResponse.json({
      message: "Booking updated successfully",
      id: bookingId,
    });
  } catch (error) {
    console.error("Booking update error:", error);
    return NextResponse.json(
      { error: "Failed to update booking" },
      { status: 500 }
    );
  }
}

// DELETE - Delete a booking
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const { id } = await params;

  try {
    const bookingId = parseInt(id);

    const result = await query("DELETE FROM bookings WHERE id = $1", [
      bookingId,
    ]);

    if (result.rowCount === 0) {
      return NextResponse.json({ error: "Booking not found" }, { status: 404 });
    }

    return NextResponse.json({
      message: "Booking deleted successfully",
    });
  } catch (error) {
    console.error("Booking delete error:", error);
    return NextResponse.json(
      { error: "Failed to delete booking" },
      { status: 500 }
    );
  }
}
