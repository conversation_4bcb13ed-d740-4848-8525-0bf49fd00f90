"use client";

import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Calendar, User, ArrowRight } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

interface BlogPost {
  id: number;
  title: string;
  excerpt: string;
  author: string;
  date: string;
  image: string;
  slug: string;
}

export default function BlogSection() {
  const { t } = useLanguage();
  const [posts, setPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);

  const fetchPosts = async () => {
    try {
      console.log("🌐 FETCHING BLOG POSTS FOR PUBLIC SITE");

      // Add cache busting timestamp
      const timestamp = Date.now();
      const response = await fetch(`/api/blog?t=${timestamp}`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "Cache-Control": "no-cache, no-store, must-revalidate",
          Pragma: "no-cache",
          Expires: "0",
        },
        cache: "no-store", // Force fresh data
      });

      console.log("📊 BLOG API RESPONSE STATUS:", response.status);

      if (response.ok) {
        const data = await response.json();
        console.log("📋 FETCHED BLOG POSTS:", data.length, "posts");
        console.log("🎯 POSTS DATA:", data);
        setPosts(data.slice(0, 3)); // Show only first 3 posts
      } else {
        console.error("❌ FAILED TO FETCH BLOG POSTS:", response.status);
      }
    } catch (error) {
      console.error("💥 BLOG FETCH ERROR:", error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchPosts();
  }, []);

  return (
    <section id="blog" className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-50">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-16">
          <h2 className="text-3xl sm:text-5xl font-bold text-gray-900 mb-6">
            {t("blog.title")}
          </h2>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            {t("blog.subtitle")}
          </p>
        </div>

        {loading ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(3)].map((_, i) => (
              <Card key={i} className="overflow-hidden shadow-lg animate-pulse">
                <div className="w-full h-48 bg-gray-300"></div>
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-300 rounded w-3/4 mb-4"></div>
                  <div className="h-3 bg-gray-300 rounded w-full mb-2"></div>
                  <div className="h-3 bg-gray-300 rounded w-5/6"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : posts.length > 0 ? (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {posts.map((post) => (
              <Card
                key={post.id}
                className="overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300 flex flex-col"
              >
                <div className="relative w-full h-48">
                  <img
                    src={post.image || "/placeholder.jpg"} // Fallback image
                    alt={post.title}
                    className="w-full h-full object-cover"
                  />
                </div>
                <CardContent className="p-6 flex-grow flex flex-col">
                  <h3 className="text-xl font-bold text-gray-900 mb-3 flex-grow">
                    {post.title}
                  </h3>
                  <p className="text-gray-600 mb-4 text-sm">{post.excerpt}</p>
                  <div className="text-xs text-gray-500 flex items-center justify-between mt-auto">
                    <div className="flex items-center">
                      <User className="h-3 w-3 mr-1.5" />
                      <span>{post.author}</span>
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-3 w-3 mr-1.5" />
                      <span>{new Date(post.date).toLocaleDateString()}</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : (
          <div className="text-center py-12">
            <h3 className="text-xl font-semibold text-gray-800">
              No Blog Posts Yet
            </h3>
            <p className="text-gray-500 mt-2">
              Check back later for updates from our travels!
            </p>
          </div>
        )}

        {posts.length > 0 && (
          <div className="text-center mt-16">
            <Button
              variant="outline"
              className="group transition-all duration-300"
              style={{
                color: "#f00d45",
                borderColor: "#f00d45",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#f00d45";
                e.currentTarget.style.color = "white";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "transparent";
                e.currentTarget.style.color = "#f00d45";
              }}
            >
              {t("blog.viewAll")}
              <ArrowRight className="h-4 w-4 ml-2 transform group-hover:translate-x-1 transition-transform" />
            </Button>
          </div>
        )}
      </div>
    </section>
  );
}
