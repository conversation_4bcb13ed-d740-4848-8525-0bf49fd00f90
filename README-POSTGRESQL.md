# Freedom to Travel - PostgreSQL Setup

## 🎉 Sistema Configurado com PostgreSQL Real!

O sistema agora está configurado para usar PostgreSQL como banco de dados real, com dados persistidos e funcionando corretamente.

## 🐳 Docker Setup

### Iniciar o Banco de Dados
```bash
# Iniciar PostgreSQL com Docker
docker-compose up -d

# Verificar se está rodando
docker ps

# Ver logs do banco
docker logs freedom-travel-db
```

### Parar o Banco de Dados
```bash
# Parar o banco
docker-compose down

# Parar e remover volumes (apaga dados)
docker-compose down -v
```

## 🚀 Executar o Projeto

```bash
# Instalar dependências (se necessário)
npm install

# Iniciar o servidor de desenvolvimento
npm run dev

# Acessar a aplicação
# http://localhost:3000
```

## 📊 Dados de Exemplo

O banco já vem com dados de exemplo:

### Bookings (3 registros iniciais)
- <PERSON> - Paris, França (pending)
- <PERSON> - <PERSON>, <PERSON><PERSON><PERSON> (confirmed)
- <PERSON> - <PERSON>, EUA (pending)

### Blog Posts (3 posts iniciais)
- As 10 Melhores Praias do Brasil
- Guia Completo: Como Planejar sua Primeira Viagem Internacional
- Destinos Imperdíveis para 2024

### Contact Messages (3 mensagens iniciais)
- Ana Costa - Dúvida sobre pacotes
- Pedro Lima - Cancelamento de viagem
- Lucia Ferreira - Elogio ao atendimento

## 🔧 Configuração do Banco

### Credenciais
- **Host:** localhost
- **Port:** 5432
- **Database:** freedom_travel
- **User:** admin
- **Password:** admin123

### Variáveis de Ambiente (.env.local)
```env
DB_HOST=localhost
DB_PORT=5432
DB_NAME=freedom_travel
DB_USER=admin
DB_PASSWORD=admin123
```

## 🧪 Testes das APIs

### Bookings
```bash
# Listar bookings
curl http://localhost:3000/api/bookings

# Criar novo booking
curl -X POST http://localhost:3000/api/bookings \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Novo Cliente",
    "email": "<EMAIL>",
    "phone": "+55 11 99999-9999",
    "destination": "Roma, Itália",
    "startDate": "2024-10-15",
    "endDate": "2024-10-22",
    "travelers": 2,
    "requirements": "Lua de mel"
  }'

# Atualizar status
curl -X PATCH http://localhost:3000/api/bookings/1 \
  -H "Content-Type: application/json" \
  -d '{"status": "confirmed"}'
```

### Blog
```bash
# Listar posts
curl http://localhost:3000/api/blog

# Criar novo post
curl -X POST http://localhost:3000/api/blog \
  -H "Content-Type: application/json" \
  -d '{
    "title": "Novo Post",
    "content": "Conteúdo do post...",
    "excerpt": "Resumo do post",
    "author": "Admin",
    "image": "https://example.com/image.jpg"
  }'
```

## 🔍 Acesso Direto ao Banco

```bash
# Conectar ao PostgreSQL
docker exec -it freedom-travel-db psql -U admin -d freedom_travel

# Comandos SQL úteis
SELECT * FROM bookings;
SELECT * FROM blog_posts;
SELECT * FROM contact_messages;
```

## ✅ Funcionalidades Testadas

### ✅ Bookings
- [x] Listagem de bookings
- [x] Criação de novos bookings
- [x] Atualização de status
- [x] Edição de bookings
- [x] Exclusão de bookings
- [x] Persistência no PostgreSQL

### ✅ Blog
- [x] Listagem de posts
- [x] Criação de novos posts
- [x] Upload de imagens
- [x] Persistência no PostgreSQL

### ✅ Admin Dashboard
- [x] Login de administrador
- [x] Gestão de bookings
- [x] Gestão de blog posts
- [x] Troca de status em tempo real
- [x] Interface responsiva

## 🎯 Status do Sistema

**✅ SISTEMA TOTALMENTE FUNCIONAL COM POSTGRESQL!**

- ✅ Banco PostgreSQL rodando no Docker
- ✅ APIs conectadas ao PostgreSQL
- ✅ Dados persistidos corretamente
- ✅ Interface web funcionando
- ✅ Admin dashboard operacional
- ✅ Troca de status funcionando
- ✅ Criação de novos registros funcionando

## 🔄 Próximos Passos

1. **Testar a interface web** em http://localhost:3000
2. **Acessar admin dashboard** em http://localhost:3000/admin
3. **Criar novos bookings** através do formulário
4. **Gerenciar posts do blog** no painel admin
5. **Testar mudanças de status** dos bookings

## 📝 Notas Importantes

- Os dados agora são **persistidos permanentemente** no PostgreSQL
- O sistema **não perde dados** ao reiniciar
- Todas as **operações CRUD** estão funcionando
- A **troca de status** está operacional
- O **sistema está pronto para produção**
