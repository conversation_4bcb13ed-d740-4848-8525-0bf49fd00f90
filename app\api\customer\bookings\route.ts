import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import jwt from "jsonwebtoken";

export const dynamic = "force-dynamic";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    return jwt.verify(token, JWT_SECRET) as {
      customerId: number;
      email: string;
    };
  } catch (error) {
    return null;
  }
}

// Get customer's bookings
export async function GET(request: NextRequest) {
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const result = await query(
      `SELECT b.*, p.amount as payment_amount, p.status as payment_status_detail
       FROM bookings b
       LEFT JOIN payments p ON b.id = p.booking_id
       WHERE b.customer_id = $1
       ORDER BY b.created_at DESC`,
      [decoded.customerId]
    );

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Get customer bookings error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}

// Create new booking
export async function POST(request: NextRequest) {
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    const { destination, startDate, endDate, travelers, requirements } =
      await request.json();

    // Get customer info
    const customerResult = await query(
      "SELECT name, email, phone FROM customers WHERE id = $1",
      [decoded.customerId]
    );

    if (customerResult.rows.length === 0) {
      return NextResponse.json(
        { message: "Customer not found" },
        { status: 404 }
      );
    }

    const customer = customerResult.rows[0];

    // Calculate price (simple calculation - $1000 per person per week)
    const days = Math.ceil(
      (new Date(endDate).getTime() - new Date(startDate).getTime()) /
        (1000 * 60 * 60 * 24)
    );
    const weeks = Math.max(1, Math.ceil(days / 7));
    const basePrice = travelers * weeks * 1000;

    const result = await query(
      `INSERT INTO bookings (customer_id, name, email, phone, destination, start_date, end_date, travelers, requirements, price, status, payment_status)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
       RETURNING *`,
      [
        decoded.customerId,
        customer.name,
        customer.email,
        customer.phone,
        destination,
        startDate,
        endDate,
        travelers,
        requirements || "",
        basePrice,
        "confirmed", // Automatically confirmed
        "unpaid",
      ]
    );

    // Create notification for customer
    await query(
      `INSERT INTO notifications (customer_id, booking_id, type, title, message)
       VALUES ($1, $2, $3, $4, $5)`,
      [
        decoded.customerId,
        result.rows[0].id,
        "booking_confirmed",
        "Booking Confirmed!",
        `Your booking for ${destination} has been automatically confirmed. You can now proceed with payment.`,
      ]
    );

    return NextResponse.json({
      booking: result.rows[0],
      message:
        "Booking confirmed successfully! You can now proceed with payment.",
    });
  } catch (error) {
    console.error("Create booking error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
