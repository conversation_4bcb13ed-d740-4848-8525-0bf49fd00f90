"use client";

import { useState, useRef, useEffect } from "react";
import { createPortal } from "react-dom";
import { ChevronDown, Check, Loader2 } from "lucide-react";
import { Button } from "./button";
import { Badge } from "./badge";

interface StatusDropdownProps {
  currentStatus: string;
  bookingId: number;
  onStatusChange: (bookingId: number, newStatus: string) => void;
  isUpdating?: boolean;
}

const statusOptions = [
  { value: "pending", label: "Pending", variant: "secondary" as const },
  { value: "confirmed", label: "Confirmed", variant: "default" as const },
  { value: "cancelled", label: "Cancelled", variant: "destructive" as const },
  { value: "completed", label: "Completed", variant: "outline" as const },
];

export function StatusDropdown({
  currentStatus,
  bookingId,
  onStatusChange,
  isUpdating = false,
}: StatusDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
  const buttonRef = useRef<HTMLButtonElement>(null);

  console.log(
    `🎯 StatusDropdown rendered - BookingID: ${bookingId}, CurrentStatus: ${currentStatus}, IsUpdating: ${isUpdating}`
  );

  const currentOption =
    statusOptions.find((option) => option.value === currentStatus) ||
    statusOptions[0];

  const calculateDropdownPosition = () => {
    if (!buttonRef.current) return;

    const rect = buttonRef.current.getBoundingClientRect();
    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
    const scrollLeft =
      window.pageXOffset || document.documentElement.scrollLeft;

    // Ensure dropdown is visible on screen
    const dropdownWidth = 140;
    const dropdownHeight = statusOptions.length * 40; // Approximate height

    let top = rect.bottom + scrollTop + 4;
    let left = rect.left + scrollLeft;

    // Adjust if dropdown would go off screen
    if (left + dropdownWidth > window.innerWidth) {
      left = window.innerWidth - dropdownWidth - 10;
    }

    if (top + dropdownHeight > window.innerHeight + scrollTop) {
      top = rect.top + scrollTop - dropdownHeight - 4;
    }

    setDropdownPosition({ top, left });
  };

  const handleToggleDropdown = () => {
    console.log(
      `🎯 Toggle dropdown clicked - BookingID: ${bookingId}, Current isOpen: ${isOpen}`
    );

    if (!isOpen) {
      console.log("📍 Calculating dropdown position...");
      calculateDropdownPosition();
    }

    const newIsOpen = !isOpen;
    console.log(`🔄 Setting isOpen to: ${newIsOpen}`);
    setIsOpen(newIsOpen);
  };

  const handleStatusSelect = (newStatus: string) => {
    console.log(
      `StatusDropdown: Selecting status ${newStatus} for booking ${bookingId}`
    );
    console.log(`Current status: ${currentStatus}, New status: ${newStatus}`);

    if (newStatus !== currentStatus) {
      console.log("Status is different, calling onStatusChange");
      try {
        onStatusChange(bookingId, newStatus);
      } catch (error) {
        console.error("Error calling onStatusChange:", error);
      }
    } else {
      console.log("Status is the same, not calling onStatusChange");
    }
    setIsOpen(false);
  };

  // Close dropdown when clicking outside or scrolling
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        buttonRef.current &&
        !buttonRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    const handleScroll = () => {
      if (isOpen) {
        calculateDropdownPosition();
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
      window.addEventListener("scroll", handleScroll, true);
      window.addEventListener("resize", handleScroll);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      window.removeEventListener("scroll", handleScroll, true);
      window.removeEventListener("resize", handleScroll);
    };
  }, [isOpen]);

  return (
    <div className="relative inline-block">
      <Button
        ref={buttonRef}
        variant="ghost"
        size="sm"
        onClick={handleToggleDropdown}
        disabled={isUpdating}
        className="p-0 h-auto hover:bg-transparent"
      >
        <div className="flex items-center space-x-1">
          <Badge variant={currentOption.variant} className="text-xs">
            {isUpdating ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Updating...
              </>
            ) : (
              currentOption.label
            )}
          </Badge>
          {!isUpdating && <ChevronDown className="h-3 w-3 text-gray-400" />}
        </div>
      </Button>

      {isOpen &&
        !isUpdating &&
        typeof window !== "undefined" &&
        createPortal(
          <>
            {/* Backdrop */}
            <div
              className="fixed inset-0 z-[99999] bg-transparent"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log("Backdrop clicked, closing dropdown");
                setIsOpen(false);
              }}
            />

            {/* Dropdown */}
            <div
              className="fixed bg-white border border-slate-200 rounded-md shadow-2xl z-[99999] min-w-[140px] max-w-[200px]"
              style={{
                top: `${dropdownPosition.top}px`,
                left: `${dropdownPosition.left}px`,
              }}
              onClick={(e) => {
                e.stopPropagation();
              }}
            >
              {statusOptions.map((option) => (
                <button
                  key={option.value}
                  onClick={(e) => {
                    e.preventDefault();
                    e.stopPropagation();
                    console.log(`Button clicked for status: ${option.value}`);
                    handleStatusSelect(option.value);
                  }}
                  className="w-full text-left px-3 py-2 text-sm hover:bg-slate-50 flex items-center justify-between transition-colors first:rounded-t-md last:rounded-b-md"
                >
                  <Badge variant={option.variant} className="text-xs">
                    {option.label}
                  </Badge>
                  {option.value === currentStatus && (
                    <Check
                      className="h-3 w-3 ml-2 flex-shrink-0"
                      style={{ color: "#f00d45" }}
                    />
                  )}
                </button>
              ))}
            </div>
          </>,
          document.body
        )}
    </div>
  );
}
