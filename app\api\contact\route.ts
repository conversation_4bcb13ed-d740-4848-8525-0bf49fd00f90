import { NextRequest, NextResponse } from "next/server";
import { getDatabase } from "@/lib/db";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const db = getDatabase();

    const messages = await db
      .prepare(
        `
      SELECT * FROM contact_messages
      ORDER BY created_at DESC
    `
      )
      .all([]);

    return NextResponse.json(messages);
  } catch (error) {
    console.error("Contact API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch contact messages" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const db = getDatabase();
    const body = await request.json();
    const { name, email, message } = body;

    const result = await db
      .prepare(
        `
      INSERT INTO contact_messages (name, email, message)
      VALUES (?, ?, ?)
    `
      )
      .run([name, email, message]);

    return NextResponse.json({
      id: result.lastInsertRowid,
      message: "Message sent successfully",
    });
  } catch (error) {
    console.error("Contact message creation error:", error);
    return NextResponse.json(
      { error: "Failed to send message" },
      { status: 500 }
    );
  }
}
