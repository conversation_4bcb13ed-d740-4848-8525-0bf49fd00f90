import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import crypto from "crypto";
import jwt from "jsonwebtoken";

export const dynamic = "force-dynamic";

const JWT_SECRET = process.env.JWT_SECRET || "your-secret-key";

function verifyToken(request: NextRequest) {
  const authHeader = request.headers.get("authorization");
  if (!authHeader || !authHeader.startsWith("Bearer ")) {
    return null;
  }

  const token = authHeader.substring(7);
  try {
    return jwt.verify(token, JWT_SECRET) as {
      customerId: number;
      email: string;
    };
  } catch (error) {
    return null;
  }
}

export async function POST(request: NextRequest) {
  try {
    const decoded = verifyToken(request);
    if (!decoded) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 });
    }

    // Get customer info
    const customerResult = await query(
      "SELECT id, name, email, email_verified FROM customers WHERE id = $1",
      [decoded.customerId]
    );

    if (customerResult.rows.length === 0) {
      return NextResponse.json(
        { message: "Customer not found" },
        { status: 404 }
      );
    }

    const customer = customerResult.rows[0];

    if (customer.email_verified) {
      return NextResponse.json(
        { message: "Email is already verified" },
        { status: 400 }
      );
    }

    // Generate verification token
    const verificationToken = crypto.randomBytes(32).toString("hex");

    // Save verification token to database
    await query(
      `UPDATE customers 
       SET verification_token = $1, updated_at = CURRENT_TIMESTAMP
       WHERE id = $2`,
      [verificationToken, customer.id]
    );

    // Create verification link
    const verificationLink = `${process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000'}/customer/verify-email?token=${verificationToken}`;
    
    console.log(`📧 EMAIL VERIFICATION LINK for ${customer.email}:`);
    console.log(`🔗 ${verificationLink}`);

    // TODO: Send verification email
    // await sendVerificationEmail(customer.email, customer.name, verificationLink);

    return NextResponse.json({
      message: "Verification email sent successfully",
      // In development, include the verification link
      ...(process.env.NODE_ENV === 'development' && { 
        verificationLink,
        note: "In development mode - check console for verification link" 
      })
    });

  } catch (error) {
    console.error("Send verification error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
