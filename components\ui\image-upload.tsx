"use client";

import { useState, useRef } from "react";
import { Upload, Link, X, Image as ImageIcon } from "lucide-react";
import { Button } from "./button";
import { Input } from "./input";
import { Label } from "./label";
import { toast } from "sonner";

interface ImageUploadProps {
  value: string;
  onChange: (value: string) => void;
  label?: string;
  required?: boolean;
}

export function ImageUpload({
  value,
  onChange,
  label = "Image",
  required = false,
}: ImageUploadProps) {
  const [isUploading, setIsUploading] = useState(false);
  const [uploadMode, setUploadMode] = useState<"url" | "file">("url");
  const fileInputRef = useRef<HTMLInputElement>(null);

  const handleFileUpload = async (
    event: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setIsUploading(true);
    try {
      const formData = new FormData();
      formData.append("file", file);

      const response = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (response.ok) {
        onChange(data.imageUrl);
        toast.success("Image uploaded successfully!");
      } else {
        throw new Error(data.error || "Upload failed");
      }
    } catch (error) {
      console.error("Upload error:", error);
      toast.error(
        error instanceof Error ? error.message : "Failed to upload image"
      );
    } finally {
      setIsUploading(false);
      // Reset file input
      if (fileInputRef.current) {
        fileInputRef.current.value = "";
      }
    }
  };

  const handleRemoveImage = () => {
    onChange("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  return (
    <div className="space-y-4">
      <Label className="text-gray-700 font-medium">
        {label} {required && <span className="text-red-500">*</span>}
      </Label>

      {/* Mode Toggle */}
      <div className="flex space-x-2 mb-4">
        <Button
          type="button"
          variant={uploadMode === "url" ? "default" : "outline"}
          size="sm"
          onClick={() => setUploadMode("url")}
          className="flex items-center"
        >
          <Link className="h-4 w-4 mr-2" />
          URL
        </Button>
        <Button
          type="button"
          variant={uploadMode === "file" ? "default" : "outline"}
          size="sm"
          onClick={() => setUploadMode("file")}
          className="flex items-center"
        >
          <Upload className="h-4 w-4 mr-2" />
          Upload
        </Button>
      </div>

      {/* URL Input Mode */}
      {uploadMode === "url" && (
        <Input
          value={value}
          onChange={(e) => onChange(e.target.value)}
          placeholder="https://images.pexels.com/..."
          className="modal-input bg-white border-slate-300 text-gray-900 placeholder-gray-500"
        />
      )}

      {/* File Upload Mode */}
      {uploadMode === "file" && (
        <div className="space-y-3">
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileUpload}
            className="hidden"
            disabled={isUploading}
          />

          <Button
            type="button"
            variant="outline"
            onClick={() => fileInputRef.current?.click()}
            disabled={isUploading}
            className="w-full h-32 border-2 border-dashed border-slate-300 flex flex-col items-center justify-center space-y-2 text-gray-600 transition-colors"
            onMouseEnter={(e) => {
              e.currentTarget.style.borderColor = "#f00d45";
              e.currentTarget.style.color = "#f00d45";
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.borderColor = "#cbd5e1";
              e.currentTarget.style.color = "#6b7280";
            }}
          >
            {isUploading ? (
              <>
                <div
                  className="animate-spin rounded-full h-8 w-8 border-b-2"
                  style={{ borderColor: "#f00d45" }}
                ></div>
                <span>Uploading...</span>
              </>
            ) : (
              <>
                <Upload className="h-8 w-8" />
                <span>Click to upload image</span>
                <span className="text-sm text-gray-500">
                  PNG, JPG, WebP up to 5MB
                </span>
              </>
            )}
          </Button>
        </div>
      )}

      {/* Image Preview */}
      {value && (
        <div className="relative">
          <div className="border border-slate-200 rounded-lg p-4 bg-slate-50">
            <div className="flex items-start space-x-3">
              <ImageIcon className="h-5 w-5 text-gray-500 mt-1 flex-shrink-0" />
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-gray-900 truncate">
                  Image selected
                </p>
                <p className="text-sm text-gray-500 truncate">{value}</p>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="sm"
                onClick={handleRemoveImage}
                className="text-red-600 hover:text-red-700 hover:bg-red-50 flex-shrink-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Image Preview */}
          <div className="mt-3">
            <img
              src={value}
              alt="Preview"
              className="max-w-full h-32 object-cover rounded-lg border border-slate-200"
              onError={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.display = "none";
              }}
            />
          </div>
        </div>
      )}
    </div>
  );
}
