'use client';

import React, { createContext, useContext, useState, useEffect } from 'react';

type Language = 'en' | 'no' | 'es';

interface LanguageContextType {
  language: Language;
  setLanguage: (lang: Language) => void;
  t: (key: string) => string;
}

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

const translations = {
  en: {
    // Navigation
    'nav.home': 'Home',
    'nav.services': 'Services',
    'nav.about': 'About',
    'nav.blog': 'Blog',
    'nav.booking': 'Booking',
    'nav.contact': 'Contact',
    'nav.bookNow': 'Book Now',
    'nav.admin': 'Admin',
    
    // Hero Section
    'hero.title': 'A World Without Limits',
    'hero.subtitle': 'Unlock extraordinary travel experiences tailored for wheelchair users. Discover accessible accommodations, personalized itineraries and breathtaking locations with Freedom To Travel!',
    'hero.accessibleDestinations': 'Accessible Destinations',
    'hero.happyTravelers': 'Happy Travelers',
    'hero.averageRating': 'Average Rating',
    
    // Services
    'services.title': 'Our Services',
    'services.subtitle': 'Comprehensive accessibility solutions for your perfect journey',
    'services.accommodation.title': 'Accessible Accommodations',
    'services.accommodation.description': 'Carefully vetted hotels and venues with full wheelchair accessibility',
    'services.transport.title': 'Accessible Transportation',
    'services.transport.description': 'Specialized vehicles and transfer services designed for comfort',
    'services.itinerary.title': 'Custom Itineraries',
    'services.itinerary.description': 'Personalized travel plans crafted around your needs and interests',
    'services.support.title': '24/7 Support',
    'services.support.description': 'Round-the-clock assistance throughout your journey',
    
    // About
    'about.title': 'About Freedom to Travel',
    'about.description': 'We believe that everyone deserves to explore the world without barriers. Our mission is to make travel accessible, enjoyable, and unforgettable for wheelchair users and people with mobility challenges.',
    'about.passionate.title': 'Passionate About Accessibility',
    'about.passionate.description': 'Every destination we recommend has been personally tested for wheelchair accessibility.',
    'about.safety.title': 'Safety First',
    'about.safety.description': 'Your safety and comfort are our top priorities in every travel arrangement.',
    'about.award.title': 'Award-Winning Service',
    'about.award.description': 'Recognized for excellence in accessible tourism and customer satisfaction.',
    
    // Blog
    'blog.title': 'Travel Insights',
    'blog.subtitle': 'Stories, tips, and guides for accessible travel',
    'blog.viewAll': 'View All Posts',
    
    // Booking
    'booking.title': 'Book Your Journey',
    'booking.subtitle': 'Start planning your accessible adventure today',
    'booking.form.name': 'Full Name',
    'booking.form.email': 'Email Address',
    'booking.form.phone': 'Phone Number',
    'booking.form.destination': 'Preferred Destination',
    'booking.form.startDate': 'Start Date',
    'booking.form.endDate': 'End Date',
    'booking.form.travelers': 'Number of Travelers',
    'booking.form.requirements': 'Special Requirements',
    'booking.form.requirementsPlaceholder': 'e.g., wheelchair accessible room, dietary needs',
    'booking.form.submit': 'Submit Booking Request',
    
    // Contact
    'contact.title': 'Get in Touch',
    'contact.subtitle': 'Have questions? We\'re here to help plan your perfect accessible journey',
    'contact.getInTouch': 'Get In Touch',
    'contact.email': 'Email',
    'contact.phone': 'Phone',
    'contact.office': 'Office',
    'contact.officeHours': 'Office Hours',
    'contact.weekdays': 'Monday - Friday',
    'contact.saturday': 'Saturday',
    'contact.sunday': 'Sunday',
    'contact.sendMessage': 'Send us a Message',
    'contact.form.name': 'Your Name',
    'contact.form.email': 'Your Email',
    'contact.form.message': 'Your Message',
    'contact.form.submit': 'Send Message',
    
    // Common
    'common.bookNow': 'Book Now',
    'common.learnMore': 'Learn More',
    'common.readMore': 'Read More',

    // Footer
    'footer.description': 'Making travel accessible and unforgettable for everyone, regardless of mobility challenges.',
    'footer.services': 'Services',
    'footer.accessibleAccommodations': 'Accessible Accommodations',
    'footer.transport': 'Wheelchair-Friendly Transport',
    'footer.itineraries': 'Custom Itineraries',
    'footer.support': '24/7 Support',
    'footer.contact': 'Contact',
    'footer.rights': '© 2025 Freedom to Travel. All rights reserved.',
    'footer.privacy': 'Privacy Policy',
    'footer.terms': 'Terms of Service',
  },
  no: {
    // Navigation
    'nav.home': 'Hjem',
    'nav.services': 'Tjenester',
    'nav.about': 'Om Oss',
    'nav.blog': 'Blogg',
    'nav.booking': 'Booking',
    'nav.contact': 'Kontakt',
    'nav.bookNow': 'Bestill Nå',
    'nav.admin': 'Admin',
    
    // Hero Section
    'hero.title': 'En Verden Uten Grenser',
    'hero.subtitle': 'Lås opp ekstraordinære reiseopplevelser tilpasset rullestolbrukere. Oppdag tilgjengelige overnattingssteder, personlige reiseruter og fantastiske lokasjoner med Freedom To Travel!',
    'hero.accessibleDestinations': 'Tilgjengelige Destinasjoner',
    'hero.happyTravelers': 'Fornøyde Reisende',
    'hero.averageRating': 'Gjennomsnittlig Vurdering',
    
    // Services
    'services.title': 'Våre Tjenester',
    'services.subtitle': 'Omfattende tilgjengelighetsløsninger for din perfekte reise',
    'services.accommodation.title': 'Tilgjengelig Overnatting',
    'services.accommodation.description': 'Nøye utvalgte hoteller og steder med full rullestoiltilgjengelighet',
    'services.transport.title': 'Tilgjengelig Transport',
    'services.transport.description': 'Spesialiserte kjøretøy og transporttjenester designet for komfort',
    'services.itinerary.title': 'Tilpassede Reiseruter',
    'services.itinerary.description': 'Personlige reiseplaner laget rundt dine behov og interesser',
    'services.support.title': '24/7 Støtte',
    'services.support.description': 'Døgnkontinuerlig assistanse gjennom hele reisen din',
    
    // About
    'about.title': 'Om Freedom to Travel',
    'about.description': 'Vi tror at alle fortjener å utforske verden uten barrierer. Vårt oppdrag er å gjøre reising tilgjengelig, hyggelig og uforglemmelig for rullestolbrukere og personer med mobilitetsu`tfordringer.',
    'about.passionate.title': 'Lidenskapelig opptatt av tilgjengelighet',
    'about.passionate.description': 'Hver destinasjon vi anbefaler er personlig testet for rullestoltilgjengelighet.',
    'about.safety.title': 'Sikkerhet først',
    'about.safety.description': 'Din sikkerhet og komfort er våre topprioriteringer i alle reisearrangementer.',
    'about.award.title': 'Prisvinnende service',
    'about.award.description': 'Anerkjent for fremragende tilgjengelig turisme og kundetilfredshet.',
    
    // Blog
    'blog.title': 'Reiseinnsikt',
    'blog.subtitle': 'Historier, tips og guider for tilgjengelig reise',
    'blog.viewAll': 'Se Alle Innlegg',
    
    // Booking
    'booking.title': 'Bestill Din Reise',
    'booking.subtitle': 'Begynn å planlegge ditt tilgjengelige eventyr i dag',
    'booking.form.name': 'Fullt Navn',
    'booking.form.email': 'E-postadresse',
    'booking.form.phone': 'Telefonnummer',
    'booking.form.destination': 'Foretrukket Destinasjon',
    'booking.form.startDate': 'Startdato',
    'booking.form.endDate': 'Sluttdato',
    'booking.form.travelers': 'Antall Reisende',
    'booking.form.requirements': 'Spesielle Krav',
    'booking.form.requirementsPlaceholder': 'f.eks. rullestoltilgjengelig rom, diettbehov',
    'booking.form.submit': 'Send Bookingforespørsel',
    
    // Contact
    'contact.title': 'Ta Kontakt',
    'contact.subtitle': 'Har du spørsmål? Vi er her for å hjelpe deg med å planlegge din perfekte tilgjengelige reise',
    'contact.getInTouch': 'Ta Kontakt',
    'contact.email': 'E-post',
    'contact.phone': 'Telefon',
    'contact.office': 'Kontor',
    'contact.officeHours': 'Kontortider',
    'contact.weekdays': 'Mandag - Fredag',
    'contact.saturday': 'Lørdag',
    'contact.sunday': 'Søndag',
    'contact.sendMessage': 'Send oss en melding',
    'contact.form.name': 'Ditt Navn',
    'contact.form.email': 'Din E-post',
    'contact.form.message': 'Din Melding',
    'contact.form.submit': 'Send Melding',
    
    // Common
    'common.bookNow': 'Bestill Nå',
    'common.learnMore': 'Lær Mer',
    'common.readMore': 'Les Mer',

    // Footer
    'footer.description': 'Gjør reise tilgjengelig og uforglemmelig for alle, uavhengig av mobilitetsutfordringer.',
    'footer.services': 'Tjenester',
    'footer.accessibleAccommodations': 'Tilgjengelig Overnatting',
    'footer.transport': 'Rullestolvennlig Transport',
    'footer.itineraries': 'Tilpassede Reiseruter',
    'footer.support': '24/7 Støtte',
    'footer.contact': 'Kontakt',
    'footer.rights': '© 2025 Freedom to Travel. Alle rettigheter forbeholdt.',
    'footer.privacy': 'Personvern',
    'footer.terms': 'Tjenestevilkår',
  },
  es: {
    // Navigation
    'nav.home': 'Inicio',
    'nav.services': 'Servicios',
    'nav.about': 'Acerca de',
    'nav.blog': 'Blog',
    'nav.booking': 'Reserva',
    'nav.contact': 'Contacto',
    'nav.bookNow': 'Reservar Ahora',
    'nav.admin': 'Admin',
    
    // Hero Section
    'hero.title': 'Un Mundo Sin Límites',
    'hero.subtitle': 'Desbloquea experiencias de viaje extraordinarias diseñadas para usuarios de sillas de ruedas. Descubre alojamientos accesibles, itinerarios personalizados y ubicaciones impresionantes con Freedom To Travel!',
    'hero.accessibleDestinations': 'Destinos Accesibles',
    'hero.happyTravelers': 'Viajeros Felices',
    'hero.averageRating': 'Calificación Promedio',
    
    // Services
    'services.title': 'Nuestros Servicios',
    'services.subtitle': 'Soluciones de accesibilidad integral para tu viaje perfecto',
    'services.accommodation.title': 'Alojamiento Accesible',
    'services.accommodation.description': 'Hoteles y lugares cuidadosamente seleccionados con total accesibilidad para sillas de ruedas',
    'services.transport.title': 'Transporte Accesible',
    'services.transport.description': 'Vehículos especializados y servicios de traslado diseñados para la comodidad',
    'services.itinerary.title': 'Itinerarios Personalizados',
    'services.itinerary.description': 'Planes de viaje personalizados creados según tus necesidades e intereses',
    'services.support.title': 'Soporte 24/7',
    'services.support.description': 'Asistencia las 24 horas durante todo tu viaje',
    
    // About
    'about.title': 'Acerca de Freedom to Travel',
    'about.description': 'Creemos que todos merecen explorar el mundo sin barreras. Nuestra misión es hacer que los viajes sean accesibles, agradables e inolvidables para usuarios de sillas de ruedas y personas con desafíos de movilidad.',
    'about.passionate.title': 'Apasionados por la accesibilidad',
    'about.passionate.description': 'Cada destino que recomendamos ha sido probado personalmente para la accesibilidad de sillas de ruedas.',
    'about.safety.title': 'La seguridad es lo primero',
    'about.safety.description': 'Su seguridad y comodidad son nuestras principales prioridades en cada arreglo de viaje.',
    'about.award.title': 'Servicio galardonado',
    'about.award.description': 'Reconocido por la excelencia en el turismo accesible y la satisfacción del cliente.',
    
    // Blog
    'blog.title': 'Perspectivas de Viaje',
    'blog.subtitle': 'Historias, consejos y guías para viajes accesibles',
    'blog.viewAll': 'Ver Todas las Publicaciones',
    
    // Booking
    'booking.title': 'Reserva Tu Viaje',
    'booking.subtitle': 'Comienza a planificar tu aventura accesible hoy',
    'booking.form.name': 'Nombre Completo',
    'booking.form.email': 'Dirección de Correo',
    'booking.form.phone': 'Número de Teléfono',
    'booking.form.destination': 'Destino Preferido',
    'booking.form.startDate': 'Fecha de Inicio',
    'booking.form.endDate': 'Fecha de Fin',
    'booking.form.travelers': 'Número de Viajeros',
    'booking.form.requirements': 'Requisitos Especiales',
    'booking.form.requirementsPlaceholder': 'ej. habitación accesible para silla de ruedas, necesidades dietéticas',
    'booking.form.submit': 'Enviar Solicitud de Reserva',
    
    // Contact
    'contact.title': 'Ponerse en Contacto',
    'contact.subtitle': '¿Tienes preguntas? Estamos aquí para ayudarte a planificar tu viaje accesible perfecto',
    'contact.getInTouch': 'Ponerse en Contacto',
    'contact.email': 'Correo Electrónico',
    'contact.phone': 'Teléfono',
    'contact.office': 'Oficina',
    'contact.officeHours': 'Horario de Oficina',
    'contact.weekdays': 'Lunes - Viernes',
    'contact.saturday': 'Sábado',
    'contact.sunday': 'Domingo',
    'contact.sendMessage': 'Envíanos un mensaje',
    'contact.form.name': 'Tu Nombre',
    'contact.form.email': 'Tu Email',
    'contact.form.message': 'Tu Mensaje',
    'contact.form.submit': 'Enviar Mensaje',
    
    // Common
    'common.bookNow': 'Reservar Ahora',
    'common.learnMore': 'Saber Más',
    'common.readMore': 'Leer Más',

    // Footer
    'footer.description': 'Haciendo que los viajes sean accesibles e inolvidables para todos, independientemente de los desafíos de movilidad.',
    'footer.services': 'Servicios',
    'footer.accessibleAccommodations': 'Alojamiento Accesible',
    'footer.transport': 'Transporte Adaptado para Sillas de Ruedas',
    'footer.itineraries': 'Itinerarios Personalizados',
    'footer.support': 'Soporte 24/7',
    'footer.contact': 'Contacto',
    'footer.rights': '© 2025 Freedom to Travel. Todos los derechos reservados.',
    'footer.privacy': 'Política de Privacidad',
    'footer.terms': 'Términos de Servicio',
  },
};

export function LanguageProvider({ children }: { children: React.ReactNode }) {
  const [language, setLanguage] = useState<Language>('en');

  useEffect(() => {
    const savedLang = localStorage.getItem('language') as Language;
    if (savedLang && ['en', 'no', 'es'].includes(savedLang)) {
      setLanguage(savedLang);
    }
  }, []);

  const handleSetLanguage = (lang: Language) => {
    setLanguage(lang);
    localStorage.setItem('language', lang);
  };

  const t = (key: string): string => {
    return translations[language][key as keyof typeof translations[typeof language]] || key;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage: handleSetLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
}

export function useLanguage() {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
}