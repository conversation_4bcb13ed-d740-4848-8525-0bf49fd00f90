import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";
import jwt from "jsonwebtoken";

export const dynamic = "force-dynamic";

export async function GET(request: NextRequest) {
  try {
    const result = await query(
      "SELECT * FROM bookings ORDER BY created_at DESC"
    );

    return NextResponse.json(result.rows);
  } catch (error) {
    console.error("Bookings API error:", error);
    return NextResponse.json(
      { error: "Failed to fetch bookings" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      name,
      email,
      phone,
      destination,
      startDate,
      endDate,
      travelers,
      requirements,
    } = body;

    // Check if customer is logged in
    let customerId = null;
    const authHeader = request.headers.get("authorization");
    if (authHeader && authHeader.startsWith("Bearer ")) {
      const token = authHeader.substring(7);
      try {
        const decoded = jwt.verify(
          token,
          process.env.JWT_SECRET || "your-secret-key"
        ) as { customerId: number };
        customerId = decoded.customerId;
      } catch (error) {
        // Token invalid, continue as guest booking
      }
    }

    // Calculate price
    const days = Math.ceil(
      (new Date(endDate).getTime() - new Date(startDate).getTime()) /
        (1000 * 60 * 60 * 24)
    );
    const weeks = Math.max(1, Math.ceil(days / 7));
    const basePrice = travelers * weeks * 1000;

    const result = await query(
      `INSERT INTO bookings (customer_id, name, email, phone, destination, start_date, end_date, travelers, requirements, price, status, payment_status)
       VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12)
       RETURNING id`,
      [
        customerId,
        name,
        email,
        phone,
        destination,
        startDate,
        endDate,
        travelers,
        requirements,
        basePrice,
        "pending",
        "unpaid",
      ]
    );

    return NextResponse.json({
      id: result.rows[0].id,
      message: "Booking request submitted successfully",
    });
  } catch (error) {
    console.error("Booking creation error:", error);
    return NextResponse.json(
      { error: "Failed to create booking request" },
      { status: 500 }
    );
  }
}
