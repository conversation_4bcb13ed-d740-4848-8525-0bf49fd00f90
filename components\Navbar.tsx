"use client";

import { useState, useEffect } from "react";
import { createPortal } from "react-dom";
import { Button } from "@/components/ui/button";
import {
  Menu,
  X,
  Globe,
  PlaneTakeoff,
  Home,
  Briefcase,
  Users,
  BookOpen,
  Calendar,
  Phone,
} from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import Link from "next/link";

export default function Navbar() {
  const [isOpen, setIsOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [mounted, setMounted] = useState(false);
  const [isLanguageDropdownOpen, setIsLanguageDropdownOpen] = useState(false);
  const { language, setLanguage, t } = useLanguage();

  useEffect(() => {
    setMounted(true);
  }, []);

  const menuItems = [
    { key: "home", id: "hero", icon: Home },
    { key: "services", id: "services", icon: Briefcase },
    { key: "about", id: "about", icon: Users },
    { key: "blog", id: "blog", icon: BookOpen },
    { key: "contact", id: "contact", icon: Phone },
  ];

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 20);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  // Control body scroll when mobile menu is open
  useEffect(() => {
    if (isOpen) {
      // Simply prevent scrolling without changing position
      document.body.style.overflow = "hidden";
      document.body.classList.add("no-scroll");
    } else {
      // Restore scroll ability
      document.body.style.overflow = "";
      document.body.classList.remove("no-scroll");
      setIsLanguageDropdownOpen(false); // Close language dropdown when menu closes
    }

    return () => {
      document.body.style.overflow = "";
      document.body.classList.remove("no-scroll");
    };
  }, [isOpen]);

  // Close language dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (isLanguageDropdownOpen && isOpen) {
        const target = event.target as Element;
        if (!target.closest(".language-dropdown-container")) {
          setIsLanguageDropdownOpen(false);
        }
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isLanguageDropdownOpen, isOpen]);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
  };

  const closeMenu = () => {
    setIsOpen(false);
  };

  const scrollToSection = (sectionId: string) => {
    // Close the menu first
    setIsOpen(false);

    // Small delay to let menu close animation start, then scroll
    setTimeout(() => {
      const element = document.getElementById(sectionId);
      if (element) {
        const offsetTop = element.offsetTop - 80;
        window.scrollTo({
          top: offsetTop,
          behavior: "smooth",
        });
      }
    }, 100);
  };

  return (
    <nav
      className={`fixed w-full z-50 transition-all duration-500 ease-out ${
        isScrolled
          ? "bg-white/95 backdrop-blur-md shadow-lg border-b border-gray-200/20"
          : "bg-transparent"
      }`}
      style={{
        backdropFilter: isScrolled ? "blur(12px)" : "none",
      }}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <Link
            href="/"
            className={`flex items-center gap-2 font-bold text-xl cursor-pointer transition-colors duration-300 ${
              isScrolled ? "text-gray-900" : "text-white drop-shadow-lg"
            }`}
            aria-label="Ir para o topo da página inicial"
          >
            <PlaneTakeoff
              className="w-6 h-6 transition-colors duration-300"
              style={{ color: "#f00d45" }}
            />
            Freedom to Travel
          </Link>

          {/* Desktop Menu */}
          <div className="hidden md:flex items-center space-x-8">
            {menuItems.map((item) => (
              <button
                key={item.key}
                onClick={() => scrollToSection(item.id)}
                className={`transition-colors duration-300 font-medium hover:scale-105 ${
                  isScrolled
                    ? "text-gray-600"
                    : "text-white/90 hover:text-white drop-shadow-md"
                }`}
                onMouseEnter={(e) => {
                  if (isScrolled) e.currentTarget.style.color = "#f00d45";
                }}
                onMouseLeave={(e) => {
                  if (isScrolled) e.currentTarget.style.color = "#6b7280";
                }}
              >
                {t(`nav.${item.key}`)}
              </button>
            ))}

            {/* Customer Portal Link */}
            <Link
              href="/customer/auth"
              className={`transition-colors duration-300 font-medium hover:scale-105 px-3 py-1 rounded-md border ${
                isScrolled
                  ? "text-blue-600 border-blue-600 hover:bg-blue-600 hover:text-white"
                  : "text-white border-white hover:bg-white hover:text-blue-600"
              }`}
            >
              Customer Portal
            </Link>

            {/* Language Switcher */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className={`transition-colors duration-300 ${
                    isScrolled
                      ? "text-gray-600"
                      : "text-white/90 hover:text-white drop-shadow-md"
                  }`}
                  onMouseEnter={(e) => {
                    if (isScrolled) e.currentTarget.style.color = "#f00d45";
                  }}
                  onMouseLeave={(e) => {
                    if (isScrolled) e.currentTarget.style.color = "#6b7280";
                  }}
                >
                  <Globe className="h-4 w-4 mr-2" />
                  {language.toUpperCase()}
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuItem onClick={() => setLanguage("en")}>
                  English
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLanguage("no")}>
                  Norsk
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => setLanguage("es")}>
                  Español
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>

            <Button
              onClick={() => scrollToSection("booking")}
              className="text-white font-semibold px-6 py-2 rounded-lg transition-all duration-300 transform hover:scale-105 shadow-lg hover:shadow-xl"
              style={{
                backgroundColor: "#f00d45",
                boxShadow: isScrolled
                  ? "0 4px 14px 0 rgba(240, 13, 69, 0.3)"
                  : "0 4px 14px 0 rgba(0, 0, 0, 0.2)",
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#d00b3a";
                e.currentTarget.style.transform = "scale(1.05)";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "#f00d45";
                e.currentTarget.style.transform = "scale(1)";
              }}
            >
              {t("nav.bookNow")}
            </Button>
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden">
            <Button
              variant="ghost"
              size="sm"
              onClick={toggleMenu}
              className={`transition-all duration-300 hover:scale-110 ${
                isScrolled
                  ? "text-gray-600 hover:bg-gray-100"
                  : "text-white/90 hover:text-white hover:bg-white/10 drop-shadow-md"
              }`}
              style={{
                zIndex: isOpen ? 50 : 120,
                position: "relative",
              }}
              onMouseEnter={(e) => {
                if (isScrolled) e.currentTarget.style.color = "#f00d45";
              }}
              onMouseLeave={(e) => {
                if (isScrolled) e.currentTarget.style.color = "#6b7280";
              }}
            >
              <Menu className="h-6 w-6" />
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile Menu Overlay - Using Portal */}
      {mounted &&
        isOpen &&
        createPortal(
          <div
            className="fixed inset-0 md:hidden transition-all duration-500 mobile-menu-overlay bg-black/50 backdrop-blur-sm"
            onClick={closeMenu}
            style={{ zIndex: 999999 }}
          >
            <div
              className="fixed top-0 right-0 h-full bg-white backdrop-blur-xl shadow-2xl w-80 transition-all duration-500 ease-out transform translate-x-0 overflow-y-auto mobile-menu-panel"
              onClick={(e) => e.stopPropagation()}
              style={{ zIndex: 1000000 }}
            >
              {/* Header do Menu */}
              <div
                className="flex justify-between items-center p-6 border-b border-gray-200/30"
                style={{
                  background: "linear-gradient(135deg, #f00d45, #d00b3a)",
                }}
              >
                {/* Language Switcher - Left Side */}
                <div className="relative language-dropdown-container">
                  <Button
                    variant="ghost"
                    className="text-white hover:bg-white/20 transition-all duration-200 p-2 rounded-full"
                    onClick={() =>
                      setIsLanguageDropdownOpen(!isLanguageDropdownOpen)
                    }
                  >
                    <Globe
                      className="h-5 w-5 mr-1"
                      style={{ color: "white" }}
                    />
                    <span className="font-medium text-sm">
                      {language.toUpperCase()}
                    </span>
                  </Button>

                  {isLanguageDropdownOpen && (
                    <div
                      className="absolute top-full mt-2 left-0 bg-white border border-gray-200 rounded-xl shadow-lg z-[1000001] overflow-hidden min-w-[120px]"
                      style={{ zIndex: 1000001 }}
                    >
                      <button
                        className="w-full px-4 py-3 text-left text-sm hover:bg-gray-50 transition-colors text-gray-700"
                        onClick={() => {
                          setLanguage("en");
                          setIsLanguageDropdownOpen(false);
                        }}
                      >
                        English
                      </button>
                      <button
                        className="w-full px-4 py-3 text-left text-sm hover:bg-gray-50 transition-colors text-gray-700"
                        onClick={() => {
                          setLanguage("no");
                          setIsLanguageDropdownOpen(false);
                        }}
                      >
                        Norsk
                      </button>
                      <button
                        className="w-full px-4 py-3 text-left text-sm hover:bg-gray-50 transition-colors text-gray-700"
                        onClick={() => {
                          setLanguage("es");
                          setIsLanguageDropdownOpen(false);
                        }}
                      >
                        Español
                      </button>
                    </div>
                  )}
                </div>

                {/* Close Button - Right Side */}
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={closeMenu}
                  className="text-white hover:bg-white/20 transition-colors p-2 rounded-full"
                  style={{
                    zIndex: 120,
                    position: "relative",
                  }}
                >
                  <X className="h-6 w-6" />
                </Button>
              </div>

              {/* Menu Items */}
              <div className="flex flex-col p-6 space-y-3">
                {menuItems.map((item) => {
                  const IconComponent = item.icon;
                  return (
                    <button
                      key={item.key}
                      onClick={() => {
                        scrollToSection(item.id);
                      }}
                      className="group flex items-center w-full text-left py-4 px-4 text-base font-medium text-gray-700 rounded-xl hover:bg-gray-50 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-md border border-transparent hover:border-gray-200/50"
                      onMouseEnter={(e) => {
                        e.currentTarget.style.color = "#f00d45";
                      }}
                      onMouseLeave={(e) => {
                        e.currentTarget.style.color = "#374151";
                      }}
                    >
                      <IconComponent
                        className="h-5 w-5 mr-3 transition-colors duration-200"
                        style={{ color: "#f00d45" }}
                      />
                      {t(`nav.${item.key}`)}
                    </button>
                  );
                })}

                {/* Customer Portal Link */}
                <Link
                  href="/customer/auth"
                  onClick={closeMenu}
                  className="group flex items-center w-full text-left py-4 px-4 text-base font-medium text-blue-600 rounded-xl hover:bg-blue-50 transition-all duration-300 transform hover:scale-[1.02] hover:shadow-md border border-blue-200 hover:border-blue-300"
                >
                  <Users className="h-5 w-5 mr-3 text-blue-600" />
                  Customer Portal
                </Link>

                <div className="pt-6 mt-6 border-t border-gray-200/30">
                  <Button
                    onClick={() => {
                      scrollToSection("booking");
                    }}
                    className="w-full text-white font-semibold py-4 rounded-xl transition-all duration-300 transform hover:scale-[1.02] hover:shadow-lg flex items-center justify-center gap-2"
                    style={{
                      background: "linear-gradient(to right, #f00d45, #d00b3a)",
                    }}
                    onMouseEnter={(e) => {
                      e.currentTarget.style.background =
                        "linear-gradient(to right, #d00b3a, #b00932)";
                    }}
                    onMouseLeave={(e) => {
                      e.currentTarget.style.background =
                        "linear-gradient(to right, #f00d45, #d00b3a)";
                    }}
                  >
                    <Calendar className="h-5 w-5" />
                    {t("nav.bookNow")}
                  </Button>
                </div>
              </div>
            </div>
          </div>,
          document.body
        )}
    </nav>
  );
}
