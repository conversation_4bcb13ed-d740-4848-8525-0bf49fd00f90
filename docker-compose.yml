version: '3.8'

services:
  postgres:
    image: postgres:15
    container_name: freedom-travel-db
    environment:
      POSTGRES_DB: freedom_travel
      POSTGRES_USER: admin
      POSTGRES_PASSWORD: admin123
      POSTGRES_HOST_AUTH_METHOD: trust
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U admin -d freedom_travel"]
      interval: 10s
      timeout: 5s
      retries: 5

volumes:
  postgres_data:
    driver: local
