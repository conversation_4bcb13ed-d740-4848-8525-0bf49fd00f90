"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  BookOpen,
  Calendar,
  BarChart3,
  TrendingUp,
  DollarSign,
  ArrowLeft,
  LogOut,
  Home,
  Plus,
} from "lucide-react";
import { toast } from "sonner";
import { useAuth } from "@/contexts/AuthContext";
import ProtectedRoute from "@/components/ProtectedRoute";

// Types
interface Booking {
  id: number;
  name: string;
  email: string;
  phone: string;
  destination: string;
  start_date: string;
  end_date: string;
  travelers: number;
  status: string;
  created_at: string;
}

interface BlogPost {
  id: number;
  title: string;
  content: string;
  excerpt: string;
  author: string;
  image: string;
  date: string;
  published: boolean;
}

function AdminDashboard() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("bookings");
  const [currentView, setCurrentView] = useState("dashboard");

  const { logout } = useAuth();
  const router = useRouter();

  useEffect(() => {
    fetchData();
  }, []);

  const fetchData = async () => {
    try {
      const [bookingsRes, blogRes] = await Promise.all([
        fetch("/api/bookings"),
        fetch("/api/blog?admin=true"),
      ]);

      if (bookingsRes.ok) {
        const bookingsData = await bookingsRes.json();
        setBookings(bookingsData);
      }

      if (blogRes.ok) {
        const blogData = await blogRes.json();
        setBlogPosts(blogData);
      }
    } catch (error) {
      console.error("Failed to fetch data:", error);
      toast.error("Failed to load dashboard data");
    } finally {
      setLoading(false);
    }
  };

  const handleLogout = () => {
    logout();
    toast.success("Logout successful!");
    router.push("/admin/login");
  };

  const goToHome = () => {
    router.push("/");
  };

  const navigateToTab = (tabName: string) => {
    setActiveTab(tabName);
    setCurrentView("tabs");
  };

  if (loading) {
    return (
      <ProtectedRoute>
        <div className="min-h-screen bg-white flex items-center justify-center">
          <div className="text-center">
            <div
              className="animate-spin rounded-full h-12 w-12 border-b-2 mx-auto mb-4"
              style={{ borderColor: "#f00d45" }}
            ></div>
            <p className="text-gray-600">Loading dashboard...</p>
          </div>
        </div>
      </ProtectedRoute>
    );
  }

  return (
    <ProtectedRoute>
      <div className="min-h-screen bg-white">
        <div className="container mx-auto px-3 sm:px-4 py-2 sm:py-4 lg:py-6 pb-12 flex flex-col min-h-screen">
          {/* Navbar */}
          <div className="bg-white border-b border-slate-200 p-3 sm:p-4 lg:p-6 mb-2 sm:mb-4 lg:mb-6">
            <div className="flex items-center justify-between">
              {/* Logo */}
              <div className="flex items-center">
                <h1
                  className="text-xl sm:text-2xl lg:text-3xl font-bold"
                  style={{ color: "#f00d45" }}
                >
                  Freedom to Travel
                </h1>
              </div>

              {/* Desktop Navigation Buttons */}
              <div className="hidden sm:flex items-center gap-3">
                <Button
                  onClick={goToHome}
                  variant="outline"
                  className="border-blue-600 text-blue-600 hover:bg-blue-600 hover:text-white"
                >
                  <Home className="h-4 w-4 mr-2" />
                  Go to Site
                </Button>
                <Button
                  onClick={handleLogout}
                  variant="outline"
                  className="border-red-600 text-red-600 hover:bg-red-600 hover:text-white"
                >
                  <LogOut className="h-4 w-4 mr-2" />
                  Logout
                </Button>
              </div>
            </div>
          </div>

          {currentView === "dashboard" ? (
            /* Dashboard Overview */
            <div className="flex-1 flex flex-col justify-center items-center">
              <div className="text-center mb-6 sm:mb-8">
                <h2 className="text-2xl sm:text-3xl font-bold text-gray-900 mb-2">
                  Welcome to Admin Dashboard
                </h2>
                <p className="text-gray-600 text-sm sm:text-base">
                  Manage your travel business efficiently
                </p>
              </div>

              {/* Dashboard Cards */}
              <div className="flex justify-center items-center w-full px-4 sm:px-6 lg:px-8 mb-8">
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 sm:gap-6 w-full max-w-6xl mx-auto">
                  {/* Bookings Card */}
                  <Card
                    className="bg-gradient-to-r from-blue-500 to-blue-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group"
                    onClick={() => navigateToTab("bookings")}
                  >
                    <CardContent className="p-4 sm:p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-blue-100 text-sm font-medium mb-2">
                            Total Bookings
                          </p>
                          <p className="text-2xl font-bold mb-1">
                            {bookings.length}
                          </p>
                          <p className="text-xs text-blue-100">
                            {
                              bookings.filter((b) => b.status === "confirmed")
                                .length
                            }{" "}
                            confirmed
                          </p>
                        </div>
                        <Calendar className="h-8 w-8 text-blue-200" />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Blog Posts Card */}
                  <Card
                    className="bg-gradient-to-r from-purple-500 to-purple-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group"
                    onClick={() => navigateToTab("blog")}
                  >
                    <CardContent className="p-4 sm:p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-purple-100 text-sm font-medium mb-2">
                            Blog Posts
                          </p>
                          <p className="text-2xl font-bold mb-1">
                            {blogPosts.length}
                          </p>
                          <p className="text-xs text-purple-100">
                            {blogPosts.filter((p) => p.published).length}{" "}
                            published
                          </p>
                        </div>
                        <BookOpen className="h-8 w-8 text-purple-200" />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Customers Card */}
                  <Card
                    className="bg-gradient-to-r from-green-500 to-green-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group"
                    onClick={() => navigateToTab("customers")}
                  >
                    <CardContent className="p-4 sm:p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-green-100 text-sm font-medium mb-2">
                            Customers
                          </p>
                          <p className="text-2xl font-bold mb-1">
                            {
                              Array.from(new Set(bookings.map((b) => b.email)))
                                .length
                            }
                          </p>
                          <p className="text-xs text-green-100">
                            Unique customers
                          </p>
                        </div>
                        <Users className="h-8 w-8 text-green-200" />
                      </div>
                    </CardContent>
                  </Card>

                  {/* Revenue Card */}
                  <Card
                    className="bg-gradient-to-r from-orange-500 to-orange-600 text-white shadow-lg hover:shadow-xl transition-all duration-300 cursor-pointer group"
                    onClick={() => navigateToTab("analytics")}
                  >
                    <CardContent className="p-4 sm:p-6">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-orange-100 text-sm font-medium mb-2">
                            Revenue
                          </p>
                          <p className="text-2xl font-bold mb-1">
                            $
                            {(
                              bookings.filter((b) => b.status === "confirmed")
                                .length * 2500
                            ).toLocaleString()}
                          </p>
                          <p className="text-xs text-orange-100">
                            {Math.round(
                              (bookings.filter((b) => b.status === "confirmed")
                                .length /
                                Math.max(bookings.length, 1)) *
                                100
                            )}
                            % conversion
                          </p>
                        </div>
                        <DollarSign className="h-8 w-8 text-orange-200" />
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            </div>
          ) : (
            /* Tabs View */
            <>
              {/* Mobile Tabs */}
              <div className="block lg:hidden">
                <Tabs
                  value={activeTab}
                  onValueChange={setActiveTab}
                  className="h-screen overflow-hidden"
                >
                  <TabsList className="grid w-full grid-cols-5 bg-white border-slate-200 h-12 p-1 mb-2 relative z-50">
                    <Button
                      onClick={() => setCurrentView("dashboard")}
                      className="bg-transparent hover:bg-blue-600 hover:text-white text-blue-600 border-0 shadow-none flex flex-col items-center justify-center gap-1 px-1 h-full rounded-md transition-all duration-200"
                    >
                      <ArrowLeft className="h-4 w-4" />
                    </Button>
                    <TabsTrigger
                      value="bookings"
                      className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs font-medium transition-all duration-200 flex flex-col items-center justify-center gap-1 px-1"
                    >
                      <Calendar className="h-4 w-4" />
                    </TabsTrigger>
                    <TabsTrigger
                      value="blog"
                      className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs font-medium transition-all duration-200 flex flex-col items-center justify-center gap-1 px-1"
                    >
                      <BookOpen className="h-4 w-4" />
                    </TabsTrigger>
                    <TabsTrigger
                      value="customers"
                      className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs font-medium transition-all duration-200 flex flex-col items-center justify-center gap-1 px-1"
                    >
                      <Users className="h-4 w-4" />
                    </TabsTrigger>
                    <TabsTrigger
                      value="analytics"
                      className="data-[state=active]:bg-blue-600 data-[state=active]:text-white text-xs font-medium transition-all duration-200 flex flex-col items-center justify-center gap-1 px-1"
                    >
                      <BarChart3 className="h-4 w-4" />
                    </TabsTrigger>
                  </TabsList>

                  {/* Bookings Tab */}
                  <TabsContent
                    value="bookings"
                    className="h-[calc(100vh-80px)] overflow-hidden"
                  >
                    <Card className="bg-white border-slate-200 shadow-sm h-full flex flex-col">
                      <CardContent className="p-4 flex-1 flex flex-col overflow-hidden">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-semibold text-gray-900">
                            Bookings ({bookings.length})
                          </h3>
                          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                            <Plus className="h-4 w-4 mr-2" />
                            Add
                          </Button>
                        </div>
                        <div className="flex-1 overflow-y-auto">
                          {bookings.length === 0 ? (
                            <div className="text-center py-8">
                              <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                              <p className="text-gray-600">No bookings yet</p>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              {bookings.map((booking) => (
                                <div
                                  key={booking.id}
                                  className="border border-slate-200 rounded-lg p-3"
                                >
                                  <div className="flex justify-between items-start mb-2">
                                    <h4 className="font-medium text-gray-900">
                                      {booking.name}
                                    </h4>
                                    <Badge
                                      variant={
                                        booking.status === "confirmed"
                                          ? "default"
                                          : "outline"
                                      }
                                    >
                                      {booking.status}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    {booking.destination}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    {booking.start_date} - {booking.end_date}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>

                  {/* Blog Tab */}
                  <TabsContent
                    value="blog"
                    className="h-[calc(100vh-80px)] overflow-hidden"
                  >
                    <Card className="bg-white border-slate-200 shadow-sm h-full flex flex-col">
                      <CardContent className="p-4 flex-1 flex flex-col overflow-hidden">
                        <div className="flex justify-between items-center mb-4">
                          <h3 className="text-lg font-semibold text-gray-900">
                            Blog Posts ({blogPosts.length})
                          </h3>
                          <Button className="bg-blue-600 hover:bg-blue-700 text-white">
                            <Plus className="h-4 w-4 mr-2" />
                            Create
                          </Button>
                        </div>
                        <div className="flex-1 overflow-y-auto">
                          {blogPosts.length === 0 ? (
                            <div className="text-center py-8">
                              <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                              <p className="text-gray-600">No blog posts yet</p>
                            </div>
                          ) : (
                            <div className="space-y-3">
                              {blogPosts.map((post) => (
                                <div
                                  key={post.id}
                                  className="border border-slate-200 rounded-lg p-3"
                                >
                                  <div className="flex justify-between items-start mb-2">
                                    <h4 className="font-medium text-gray-900">
                                      {post.title}
                                    </h4>
                                    <Badge
                                      variant={
                                        post.published ? "default" : "outline"
                                      }
                                    >
                                      {post.published ? "Published" : "Draft"}
                                    </Badge>
                                  </div>
                                  <p className="text-sm text-gray-600">
                                    {post.excerpt}
                                  </p>
                                  <p className="text-xs text-gray-500">
                                    By {post.author}
                                  </p>
                                </div>
                              ))}
                            </div>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </TabsContent>
                </Tabs>
              </div>
            </>
          )}
        </div>
      </div>
    </ProtectedRoute>
  );
}

export default AdminDashboard;
