import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { LanguageProvider } from "@/contexts/LanguageContext";
import { AuthProvider } from "@/contexts/AuthContext";
import { CustomerAuthProvider } from "@/contexts/CustomerAuthContext";
import { Toaster } from "@/components/ui/sonner";

const inter = Inter({ subsets: ["latin"] });

export const metadata: Metadata = {
  title: "Freedom to Travel - Accessible Travel for Everyone",
  description:
    "Unlock extraordinary travel experiences tailored for wheelchair users. Discover accessible accommodations, personalized itineraries and breathtaking locations.",
  keywords:
    "accessible travel, wheelchair travel, disability travel, inclusive tourism",
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body className={inter.className + " min-h-screen flex flex-col"}>
        <AuthProvider>
          <CustomerAuthProvider>
            <LanguageProvider>
              <div className="flex-1 flex flex-col">{children}</div>
              <Toaster />
            </LanguageProvider>
          </CustomerAuthProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
