"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useCustomerAuth } from "@/contexts/CustomerAuthContext";
import {
  CreditCard,
  Calendar,
  MapPin,
  DollarSign,
  ArrowLeft,
  Download,
  User,
  LogOut,
} from "lucide-react";
import { toast } from "sonner";

interface Payment {
  id: number;
  booking_id: number;
  amount: number;
  currency: string;
  payment_method: string;
  payment_provider: string;
  provider_payment_id: string;
  status: string;
  created_at: string;
  destination: string;
  start_date: string;
  end_date: string;
}

export default function PaymentHistoryPage() {
  const router = useRouter();
  const { customer, logout } = useCustomerAuth();
  const [payments, setPayments] = useState<Payment[]>([]);
  const [loading, setLoading] = useState(true);
  const [localCustomer, setLocalCustomer] = useState<any>(null);

  useEffect(() => {
    // Check if token exists in localStorage first
    const token = localStorage.getItem("customer_token");
    if (!token) {
      router.push("/customer/auth");
      return;
    }

    // Fetch customer data directly if context is not loaded
    const fetchCustomerData = async () => {
      try {
        const response = await fetch("/api/customer/profile", {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        });

        if (response.ok) {
          const customerData = await response.json();
          setLocalCustomer(customerData);
          fetchPayments();
        } else {
          router.push("/customer/auth");
        }
      } catch (error) {
        console.error("Error fetching customer data:", error);
        router.push("/customer/auth");
      }
    };

    if (customer) {
      setLocalCustomer(customer);
      fetchPayments();
    } else {
      fetchCustomerData();
    }
  }, [customer, router]);

  const fetchPayments = async () => {
    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/customer/payments", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const data = await response.json();
        setPayments(data);
      } else {
        toast.error("Failed to fetch payment history");
      }
    } catch (error) {
      console.error("Error fetching payments:", error);
      toast.error("Failed to fetch payment history");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency || "USD",
    }).format(amount);
  };

  const getPaymentMethodIcon = (method: string) => {
    switch (method.toLowerCase()) {
      case "credit_card":
      case "card":
        return <CreditCard className="h-4 w-4" />;
      default:
        return <DollarSign className="h-4 w-4" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "completed":
        return "bg-green-100 text-green-800";
      case "pending":
        return "bg-yellow-100 text-yellow-800";
      case "failed":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  if (!localCustomer) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-xl font-semibold text-gray-900">
                Freedom to Travel
              </h1>
              <span className="ml-2 text-sm text-gray-500">
                Payment History
              </span>
            </div>
            <div className="flex items-center gap-4">
              <div className="flex items-center gap-2">
                <User className="h-4 w-4 text-gray-500" />
                <span className="text-sm text-gray-700">
                  {localCustomer.name}
                </span>
              </div>
              <Button
                onClick={logout}
                variant="outline"
                size="sm"
                className="flex items-center gap-2"
              >
                <LogOut className="h-4 w-4" />
                Logout
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Navigation */}
        <div className="mb-6">
          <Button
            onClick={() => router.push("/customer/dashboard")}
            variant="outline"
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back to Dashboard
          </Button>
        </div>

        {/* Page Header */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-2">
            Payment History
          </h2>
          <p className="text-gray-600">
            View all your payment transactions and receipts
          </p>
        </div>

        {/* Payments List */}
        <Card>
          <CardHeader>
            <div className="flex justify-between items-center">
              <CardTitle>All Payments</CardTitle>
              {payments.length > 0 && (
                <Button variant="outline" size="sm">
                  <Download className="h-4 w-4 mr-2" />
                  Export
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {loading ? (
              <div className="text-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                <p className="mt-2 text-gray-600">Loading payments...</p>
              </div>
            ) : payments.length === 0 ? (
              <div className="text-center py-8">
                <CreditCard className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">
                  No payments yet
                </h3>
                <p className="text-gray-600 mb-4">
                  You haven't made any payments yet.
                </p>
                <Button
                  onClick={() => router.push("/customer/dashboard")}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  View Bookings
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {payments.map((payment) => (
                  <div
                    key={payment.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex justify-between items-start">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <div className="flex items-center gap-2">
                            {getPaymentMethodIcon(payment.payment_method)}
                            <h3 className="font-semibold text-lg">
                              {payment.destination}
                            </h3>
                          </div>
                          <Badge className={getStatusColor(payment.status)}>
                            {payment.status}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <DollarSign className="h-4 w-4" />
                            <span>
                              {formatCurrency(payment.amount, payment.currency)}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            <span>
                              {formatDate(payment.start_date)} - {formatDate(payment.end_date)}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <CreditCard className="h-4 w-4" />
                            <span className="capitalize">
                              {payment.payment_method.replace("_", " ")}
                            </span>
                          </div>
                        </div>

                        <div className="mt-2 text-xs text-gray-500">
                          Payment ID: {payment.provider_payment_id} • 
                          Paid on {formatDate(payment.created_at)}
                        </div>
                      </div>

                      <div className="flex flex-col gap-2">
                        <Button variant="outline" size="sm">
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </main>
    </div>
  );
}
