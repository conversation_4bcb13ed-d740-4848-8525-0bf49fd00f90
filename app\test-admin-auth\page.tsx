"use client";

import { useAuth } from "@/contexts/AuthContext";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

export default function TestAdminAuth() {
  const { isAuthenticated, login, logout, loading } = useAuth();

  const handleTestLogin = () => {
    const success = login("admin", "admin123");
    console.log("Login result:", success);
    console.log("Is authenticated:", isAuthenticated);
    
    // Check localStorage and cookies
    console.log("localStorage admin_authenticated:", localStorage.getItem("admin_authenticated"));
    console.log("localStorage admin_token:", localStorage.getItem("admin_token"));
    console.log("Document cookies:", document.cookie);
  };

  const handleTestLogout = () => {
    logout();
    console.log("After logout - Is authenticated:", isAuthenticated);
    console.log("localStorage admin_authenticated:", localStorage.getItem("admin_authenticated"));
    console.log("localStorage admin_token:", localStorage.getItem("admin_token"));
    console.log("Document cookies:", document.cookie);
  };

  const handleGoToAdmin = () => {
    window.location.href = "/admin";
  };

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Test Admin Authentication</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <p><strong>Status:</strong> {isAuthenticated ? "Authenticated" : "Not Authenticated"}</p>
            <p><strong>Loading:</strong> {loading ? "Yes" : "No"}</p>
          </div>
          
          <div className="space-y-2">
            <Button onClick={handleTestLogin} className="w-full">
              Test Login (admin/admin123)
            </Button>
            
            <Button onClick={handleTestLogout} variant="outline" className="w-full">
              Test Logout
            </Button>
            
            <Button onClick={handleGoToAdmin} variant="secondary" className="w-full">
              Go to Admin Dashboard
            </Button>
          </div>
          
          <div className="text-xs text-gray-600">
            <p>Check browser console for detailed logs</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
