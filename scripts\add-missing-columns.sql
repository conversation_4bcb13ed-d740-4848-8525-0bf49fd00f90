-- Add missing columns to support new features

-- Add verification_token to customers table for email verification
ALTER TABLE customers 
ADD COLUMN IF NOT EXISTS verification_token VARCHAR(255),
ADD COLUMN IF NOT EXISTS email_verified BOOLEAN DEFAULT FALSE;

-- Add booking_id to notifications table for booking-related notifications
ALTER TABLE notifications 
ADD COLUMN IF NOT EXISTS booking_id INTEGER REFERENCES bookings(id);

-- Add currency to payments table for multi-currency support
ALTER TABLE payments 
ADD COLUMN IF NOT EXISTS currency VARCHAR(3) DEFAULT 'USD';

-- Update existing payments to have USD currency
UPDATE payments SET currency = 'USD' WHERE currency IS NULL;

-- Add indexes for better performance
CREATE INDEX IF NOT EXISTS idx_customers_verification_token ON customers(verification_token);
CREATE INDEX IF NOT EXISTS idx_notifications_booking_id ON notifications(booking_id);
CREATE INDEX IF NOT EXISTS idx_payments_currency ON payments(currency);

-- Show the updated table structures
\d customers;
\d notifications;
\d payments;
