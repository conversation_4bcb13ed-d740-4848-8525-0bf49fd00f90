"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { toast } from "sonner";

interface Customer {
  id: number;
  name: string;
  email: string;
  phone?: string;
  email_verified: boolean;
  created_at: string;
}

interface CustomerAuthContextType {
  customer: Customer | null;
  isLoading: boolean;
  login: (email: string, password: string) => Promise<boolean>;
  register: (
    name: string,
    email: string,
    phone: string,
    password: string
  ) => Promise<boolean>;
  logout: () => void;
  updateProfile: (data: Partial<Customer>) => Promise<boolean>;
  refreshCustomer: () => Promise<void>;
}

const CustomerAuthContext = createContext<CustomerAuthContextType | undefined>(
  undefined
);

export function CustomerAuthProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [customer, setCustomer] = useState<Customer | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // Check if customer is logged in on mount
  useEffect(() => {
    checkAuthStatus();
  }, []);

  const checkAuthStatus = async () => {
    try {
      const token = localStorage.getItem("customer_token");
      if (!token) {
        setIsLoading(false);
        return;
      }

      const response = await fetch("/api/customer/profile", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        try {
          const customerData = await response.json();
          setCustomer(customerData);
        } catch (jsonError) {
          console.error("Error parsing profile response:", jsonError);
          localStorage.removeItem("customer_token");
          document.cookie =
            "customer_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
        }
      } else {
        localStorage.removeItem("customer_token");
        document.cookie =
          "customer_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
      }
    } catch (error) {
      console.error("Auth check error:", error);
      localStorage.removeItem("customer_token");
      document.cookie =
        "customer_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    } finally {
      setIsLoading(false);
    }
  };

  const login = async (email: string, password: string): Promise<boolean> => {
    try {
      console.log("🌐 Making login request to API...");
      const response = await fetch("/api/customer/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ email, password }),
      });

      console.log("🌐 API Response status:", response.status);

      if (response.ok) {
        try {
          const data = await response.json();
          console.log("🌐 API Response data:", data);

          localStorage.setItem("customer_token", data.token);
          console.log("💾 Token saved to localStorage");

          // Also set cookie for middleware
          document.cookie = `customer_token=${data.token}; path=/; max-age=${
            7 * 24 * 60 * 60
          }; samesite=strict`;
          console.log("🍪 Cookie set for middleware");

          setCustomer(data.customer);
          console.log("👤 Customer data set in context");

          toast.success("Login successful!");
          return true;
        } catch (jsonError) {
          console.error("🚨 Error parsing login response:", jsonError);
          toast.error("Login response error. Please try again.");
          return false;
        }
      } else {
        try {
          const error = await response.json();
          console.log("❌ API Error:", error);
          toast.error(error.message || "Login failed");
        } catch (jsonError) {
          console.error("🚨 Error parsing error response:", jsonError);
          toast.error("Login failed");
        }
        return false;
      }
    } catch (error) {
      console.error("🚨 Login error:", error);
      toast.error("Login failed. Please try again.");
      return false;
    }
  };

  const register = async (
    name: string,
    email: string,
    phone: string,
    password: string
  ): Promise<boolean> => {
    try {
      const response = await fetch("/api/customer/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ name, email, phone, password }),
      });

      if (response.ok) {
        const data = await response.json();
        localStorage.setItem("customer_token", data.token);
        // Also set cookie for middleware
        document.cookie = `customer_token=${data.token}; path=/; max-age=${
          7 * 24 * 60 * 60
        }; samesite=strict`;
        setCustomer(data.customer);
        toast.success("Registration successful!");
        return true;
      } else {
        const error = await response.json();
        toast.error(error.message || "Registration failed");
        return false;
      }
    } catch (error) {
      console.error("Registration error:", error);
      toast.error("Registration failed. Please try again.");
      return false;
    }
  };

  const logout = () => {
    localStorage.removeItem("customer_token");
    // Also remove cookie
    document.cookie =
      "customer_token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT";
    setCustomer(null);
    toast.success("Logged out successfully!");
  };

  const updateProfile = async (data: Partial<Customer>): Promise<boolean> => {
    try {
      const token = localStorage.getItem("customer_token");
      const response = await fetch("/api/customer/profile", {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(data),
      });

      if (response.ok) {
        const updatedCustomer = await response.json();
        setCustomer(updatedCustomer);
        toast.success("Profile updated successfully!");
        return true;
      } else {
        const error = await response.json();
        toast.error(error.message || "Update failed");
        return false;
      }
    } catch (error) {
      console.error("Profile update error:", error);
      toast.error("Update failed. Please try again.");
      return false;
    }
  };

  const refreshCustomer = async () => {
    try {
      const token = localStorage.getItem("customer_token");
      if (!token) return;

      const response = await fetch("/api/customer/profile", {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      });

      if (response.ok) {
        const customerData = await response.json();
        setCustomer(customerData);
      }
    } catch (error) {
      console.error("Refresh customer error:", error);
    }
  };

  const value = {
    customer,
    isLoading,
    login,
    register,
    logout,
    updateProfile,
    refreshCustomer,
  };

  return (
    <CustomerAuthContext.Provider value={value}>
      {children}
    </CustomerAuthContext.Provider>
  );
}

export function useCustomerAuth() {
  const context = useContext(CustomerAuthContext);
  if (context === undefined) {
    throw new Error(
      "useCustomerAuth must be used within a CustomerAuthProvider"
    );
  }
  return context;
}
