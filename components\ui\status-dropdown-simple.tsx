"use client";

import { useState, useRef, useEffect } from "react";
import { ChevronDown, Check, Loader2 } from "lucide-react";
import { Button } from "./button";
import { Badge } from "./badge";

interface StatusDropdownProps {
  currentStatus: string;
  bookingId: number;
  onStatusChange: (bookingId: number, newStatus: string) => void;
  isUpdating?: boolean;
}

const statusOptions = [
  { value: "pending", label: "Pending", variant: "secondary" as const },
  { value: "confirmed", label: "Confirmed", variant: "default" as const },
  { value: "cancelled", label: "Cancelled", variant: "destructive" as const },
  { value: "completed", label: "Completed", variant: "outline" as const },
];

export function StatusDropdownSimple({
  currentStatus,
  bookingId,
  onStatusChange,
  isUpdating = false,
}: StatusDropdownProps) {
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  console.log(
    `🎯 StatusDropdownSimple rendered - BookingID: ${bookingId}, CurrentStatus: ${currentStatus}, IsUpdating: ${isUpdating}`
  );

  const currentOption =
    statusOptions.find((option) => option.value === currentStatus) ||
    statusOptions[0];

  const handleToggleDropdown = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    console.log(
      `🎯 Toggle dropdown clicked - BookingID: ${bookingId}, Current isOpen: ${isOpen}`
    );
    setIsOpen(!isOpen);
  };

  const handleStatusSelect = (newStatus: string) => {
    console.log(
      `StatusDropdownSimple: Selecting status ${newStatus} for booking ${bookingId}`
    );
    console.log(`Current status: ${currentStatus}, New status: ${newStatus}`);

    if (newStatus !== currentStatus) {
      console.log("Status is different, calling onStatusChange");
      try {
        onStatusChange(bookingId, newStatus);
      } catch (error) {
        console.error("Error calling onStatusChange:", error);
      }
    } else {
      console.log("Status is the same, not calling onStatusChange");
    }
    setIsOpen(false);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isOpen]);

  return (
    <div className="relative inline-block" ref={dropdownRef}>
      <Button
        variant="ghost"
        size="sm"
        onClick={handleToggleDropdown}
        disabled={isUpdating}
        className="p-0 h-auto hover:bg-transparent"
      >
        <div className="flex items-center space-x-1">
          <Badge variant={currentOption.variant} className="text-xs">
            {isUpdating ? (
              <>
                <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                Updating...
              </>
            ) : (
              currentOption.label
            )}
          </Badge>
          {!isUpdating && <ChevronDown className="h-3 w-3 text-gray-400" />}
        </div>
      </Button>

      {isOpen && !isUpdating && (
        <div className="absolute top-full left-0 mt-1 bg-white border border-slate-200 rounded-md shadow-xl z-50 min-w-[140px] max-w-[200px]">
          {statusOptions.map((option) => (
            <button
              key={option.value}
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                console.log(`Button clicked for status: ${option.value}`);
                handleStatusSelect(option.value);
              }}
              className="w-full text-left px-3 py-2 text-sm hover:bg-slate-50 flex items-center justify-between transition-colors first:rounded-t-md last:rounded-b-md"
            >
              <Badge variant={option.variant} className="text-xs">
                {option.label}
              </Badge>
              {option.value === currentStatus && (
                <Check
                  className="h-3 w-3 ml-2 flex-shrink-0"
                  style={{ color: "#f00d45" }}
                />
              )}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}
