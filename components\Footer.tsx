"use client";

import {
  Plane,
  Mail,
  Phone,
  MapPin,
  Facebook,
  Twitter,
  Instagram,
} from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";
import Link from "next/link";

export default function Footer() {
  const { t } = useLanguage();

  return (
    <footer className="bg-slate-50 border-t border-slate-200">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div className="space-y-4">
            <div className="flex items-center space-x-2">
              <Plane className="h-8 w-8" style={{ color: "#f00d45" }} />
              <span className="text-xl font-bold text-gray-900">
                Freedom to Travel
              </span>
            </div>
            <p className="text-gray-600 leading-relaxed">
              {t("footer.description")}
            </p>
            <div className="flex space-x-4">
              <Facebook
                className="h-5 w-5 text-gray-600 cursor-pointer transition-colors"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              />
              <Instagram
                className="h-5 w-5 text-gray-600 cursor-pointer transition-colors"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              />
            </div>
          </div>

          {/* Quick Links */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">Quick Links</h3>
            <div className="space-y-2">
              <a
                href="#services"
                className="block text-gray-600 transition-colors"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              >
                {t("nav.services")}
              </a>
              <a
                href="#about"
                className="block text-gray-600 transition-colors"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              >
                {t("nav.about")}
              </a>
              <a
                href="#blog"
                className="block text-gray-600 transition-colors"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              >
                {t("nav.blog")}
              </a>
              <a
                href="#booking"
                className="block text-gray-600 transition-colors"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              >
                {t("nav.booking")}
              </a>
            </div>
          </div>

          {/* Services */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">
              {t("footer.services")}
            </h3>
            <div className="space-y-2">
              <p className="text-gray-600">
                {t("footer.accessibleAccommodations")}
              </p>
              <p className="text-gray-600">{t("footer.transport")}</p>
              <p className="text-gray-600">{t("footer.itineraries")}</p>
              <p className="text-gray-600">{t("footer.support")}</p>
            </div>
          </div>

          {/* Contact Info */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold text-gray-900">
              {t("footer.contact")}
            </h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3">
                <Mail className="h-4 w-4" style={{ color: "#f00d45" }} />
                <span className="text-gray-600"><EMAIL></span>
              </div>
              <div className="flex items-center space-x-3">
                <Phone className="h-4 w-4" style={{ color: "#f00d45" }} />
                <span className="text-gray-600">+47 12 34 56 78</span>
              </div>
              <div className="flex items-start space-x-3">
                <MapPin className="h-4 w-4 mt-1" style={{ color: "#f00d45" }} />
                <span className="text-gray-600">
                  Stavanger
                  <br />
                  Norway
                </span>
              </div>
            </div>
          </div>
        </div>

        <div className="border-t border-slate-200 mt-12 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-600 text-sm">{t("footer.rights")}</p>
            <div className="flex space-x-6 mt-4 md:mt-0">
              <a
                href="/privacy-policy"
                className="text-gray-600 text-[10px] transition-colors"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              >
                {t("footer.privacy")}
              </a>
              <a
                href="/terms-of-service"
                className="text-gray-600 text-[10px] transition-colors"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              >
                {t("footer.terms")}
              </a>

              <Link
                href="/admin/login"
                className="text-gray-500 text-xs transition-colors opacity-75 hover:opacity-100"
                title="Acesso Administrativo"
                onMouseEnter={(e) => (e.currentTarget.style.color = "#f00d45")}
                onMouseLeave={(e) => (e.currentTarget.style.color = "#6b7280")}
              >
                Admin Login
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
