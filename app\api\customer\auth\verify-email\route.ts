import { NextRequest, NextResponse } from "next/server";
import { query } from "@/lib/postgres";

export const dynamic = "force-dynamic";

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { message: "Verification token is required" },
        { status: 400 }
      );
    }

    // Find customer with verification token
    const customerResult = await query(
      `SELECT id, name, email, email_verified 
       FROM customers 
       WHERE verification_token = $1`,
      [token]
    );

    if (customerResult.rows.length === 0) {
      return NextResponse.json(
        { message: "Invalid verification token" },
        { status: 400 }
      );
    }

    const customer = customerResult.rows[0];

    if (customer.email_verified) {
      return NextResponse.json(
        { message: "Email is already verified" },
        { status: 400 }
      );
    }

    // Mark email as verified and clear verification token
    await query(
      `UPDATE customers 
       SET email_verified = TRUE, verification_token = NULL, updated_at = CURRENT_TIMESTAMP
       WHERE id = $1`,
      [customer.id]
    );

    console.log(`📧 EMAIL VERIFIED for ${customer.email}`);

    // Create notification
    await query(
      `INSERT INTO notifications (customer_id, type, title, message)
       VALUES ($1, $2, $3, $4)`,
      [
        customer.id,
        "email_verified",
        "Email Verified",
        "Your email address has been successfully verified!"
      ]
    );

    return NextResponse.json({
      message: "Email verified successfully",
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email,
        email_verified: true
      }
    });

  } catch (error) {
    console.error("Verify email error:", error);
    return NextResponse.json(
      { message: "Internal server error" },
      { status: 500 }
    );
  }
}
