"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowR<PERSON>, MapPin, Users, Star } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

export default function HeroSection() {
  const { t } = useLanguage();

  const scrollToBooking = () => {
    const element = document.getElementById("booking");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section
      id="hero"
      className="relative h-screen min-h-screen flex items-center justify-center px-4 sm:px-6 lg:px-8 pt-16"
      style={{
        backgroundImage: `linear-gradient(rgba(0, 0, 0, 0.4), rgba(0, 0, 0, 0.4)), url('/uploads/full-shot-disabled-man-woman-vacation.jpg')`,
        backgroundSize: "cover",
        backgroundPosition: "center",
        backgroundRepeat: "no-repeat",
        backgroundAttachment: "fixed",
        minHeight: "100dvh", // Dynamic viewport height for mobile
      }}
    >
      <div className="relative max-w-7xl mx-auto text-center">
        <div className="space-y-8">
          {/* Main Headline */}
          <div className="space-y-4">
            <h1 className="text-4xl sm:text-6xl lg:text-7xl font-bold text-white leading-tight drop-shadow-lg">
              {t("hero.title")}
            </h1>
            <p className="text-xl sm:text-2xl text-white/90 max-w-4xl mx-auto leading-relaxed drop-shadow-md">
              {t("hero.subtitle")}
            </p>
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-row gap-3 sm:gap-4 justify-center items-center">
            <Button
              onClick={scrollToBooking}
              size="lg"
              className="text-white font-semibold px-6 sm:px-8 py-4 rounded-md transition-all duration-300 transform hover:scale-105 shadow-xl hover:shadow-2xl border-2"
              style={
                {
                  backgroundColor: "#f00d45",
                  borderColor: "#f00d45",
                  "--tw-shadow-color": "#f00d45",
                } as React.CSSProperties
              }
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = "#d00b3a";
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = "#f00d45";
              }}
            >
              {t("common.bookNow")}
              <ArrowRight className="ml-2 h-5 w-5" />
            </Button>
            <Button
              variant="outline"
              size="lg"
              onClick={() =>
                document
                  .getElementById("services")
                  ?.scrollIntoView({ behavior: "smooth" })
              }
              className="border-2 border-white text-white hover:bg-white hover:text-gray-900 px-6 sm:px-8 py-4 rounded-md transition-all duration-300 backdrop-blur-sm bg-white/10"
            >
              {t("common.learnMore")}
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
}
