"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Heart, Shield, Award } from "lucide-react";
import { useLanguage } from "@/contexts/LanguageContext";

export default function AboutSection() {
  const { t } = useLanguage();

  const scrollToBooking = () => {
    const element = document.getElementById("booking");
    if (element) {
      element.scrollIntoView({ behavior: "smooth" });
    }
  };

  return (
    <section id="about" className="py-20 px-4 sm:px-6 lg:px-8 bg-slate-50">
      <div className="max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
          {/* Content */}
          <div className="space-y-8">
            <div>
              <h2 className="text-3xl sm:text-5xl font-bold text-gray-900 mb-6">
                {t("about.title")}
              </h2>
              <p className="text-xl text-gray-600 leading-relaxed">
                {t("about.description")}
              </p>
            </div>

            <div className="space-y-6">
              <div className="flex items-start space-x-4">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0"
                  style={{ backgroundColor: "#f00d45" }}
                >
                  <Heart className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {t("about.passionate.title")}
                  </h3>
                  <p className="text-gray-600">
                    {t("about.passionate.description")}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0"
                  style={{ backgroundColor: "#f00d45" }}
                >
                  <Shield className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {t("about.safety.title")}
                  </h3>
                  <p className="text-gray-600">
                    {t("about.safety.description")}
                  </p>
                </div>
              </div>

              <div className="flex items-start space-x-4">
                <div
                  className="w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0"
                  style={{ backgroundColor: "#f00d45" }}
                >
                  <Award className="h-6 w-6 text-white" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-2">
                    {t("about.award.title")}
                  </h3>
                  <p className="text-gray-600">
                    {t("about.award.description")}
                  </p>
                </div>
              </div>
            </div>

            <div className="mt-8 text-center lg:text-left">
              <Button
                onClick={scrollToBooking}
                size="lg"
                className="text-white font-semibold px-8 py-4 rounded-md transition-all duration-300 transform hover:scale-105"
                style={{
                  backgroundColor: "#f00d45",
                }}
                onMouseEnter={(e) => {
                  e.currentTarget.style.backgroundColor = "#d00b3a";
                }}
                onMouseLeave={(e) => {
                  e.currentTarget.style.backgroundColor = "#f00d45";
                }}
              >
                {t("common.bookNow")}
              </Button>
            </div>
          </div>

          {/* Image */}
          <div className="relative">
            <div className="aspect-square rounded-2xl bg-white p-8 border border-slate-200 shadow-lg">
              <img
                src="https://images.pexels.com/photos/7699052/pexels-photo-7699052.jpeg"
                alt="Accessible travel"
                className="w-full h-full object-cover rounded-xl"
              />
            </div>
            <div
              className="absolute -bottom-6 -right-6 w-32 h-32 rounded-full opacity-10 blur-2xl"
              style={{ backgroundColor: "#f00d45" }}
            ></div>
          </div>
        </div>
      </div>
    </section>
  );
}
